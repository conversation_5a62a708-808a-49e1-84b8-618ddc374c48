<script setup lang="ts">
import type { Recordable } from '@vben/types';

import type { VbenFormSchema } from '@vben-core/form-ui';

import type { AuthenticationProps } from './types';

import { computed, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { $t } from '@vben/locales';

import { useVbenForm } from '@vben-core/form-ui';
import { VbenButton, VbenCheckbox } from '@vben-core/shadcn-ui';

import { Verify } from '../../components';
import Title from './auth-title.vue';
import ThirdPartyLogin from './third-party-login.vue';

interface Props extends AuthenticationProps {
  formSchema?: VbenFormSchema[];
  showCaptcha?: number;
  captchaType?: string;
  imageCaptchaApi: Function;
  behaviorCaptchaApi: Function;
  behaviorCaptchaCheckApi: Function;
}

defineOptions({
  name: 'AuthenticationLogin',
});

const props = withDefaults(defineProps<Props>(), {
  codeLoginPath: '/auth/code-login',
  forgetPasswordPath: '/auth/forget-password',
  formSchema: () => [],
  loading: false,
  qrCodeLoginPath: '/auth/qrcode-login',
  ssoLoginPath: '',
  registerPath: '/auth/register',
  showCodeLogin: true,
  showForgetPassword: true,
  showQrcodeLogin: false,
  showSsoLogin: true,
  showRegister: false,
  showRememberMe: true,
  showThirdPartyLogin: true,
  submitButtonText: '',
  subTitle: '',
  title: '',
  showCaptcha: 0,
  captchaType: 'slider',
});

const emit = defineEmits<{
  submit: [Recordable<any>];
  thirdPartyLogin: [string];
}>();

const [Form, formApi] = useVbenForm(
  reactive({
    commonConfig: {
      hideLabel: true,
      hideRequiredMark: true,
      wrapperCol: { span: 24 },
    },
    schema: computed(() => props.formSchema),
    showDefaultActions: false,
  }),
);
const router = useRouter();

const REMEMBER_ME_KEY = `REMEMBER_ME_USERNAME_${location.hostname}`;

const localUsername = localStorage.getItem(REMEMBER_ME_KEY) || '';

const rememberMe = ref(!!localUsername);
const captchaChallengeTypeList = ['blockPuzzle', 'clickWord'] as const;
const captchaChallengeType = ref<string>('blockPuzzle');

const verifyRef = ref();
let captchaResolve: ((value: any) => void) | null = null;
// let captchaReject: ((reason?: any) => void) | null = null;

async function handleSubmit() {
  const { valid } = await formApi.validate();
  if (props.showCaptcha && props.captchaType === 'pic' && !imgCaptchaCode.value) {
    imgCaptchaError.value = true;
    return;
  }
  const values = await formApi.getValues();
  if (valid) {
    if (props.showCaptcha && props.captchaType === 'slider') {
      const res = await handleVerification();
      values.captchaCode = res.captchaVerification;
    } else if (props.showCaptcha && props.captchaType === 'pic') {
      // 图形验证码验证
      values.captchaCode = imgCaptchaCode.value;
      values.captchaKey = imgCaptchaKey.value;
    }
    localStorage.setItem(REMEMBER_ME_KEY, rememberMe.value ? values?.username : '');
    emit('submit', values);
  }
}
function handleThirdPartyLogin(source: string) {
  emit('thirdPartyLogin', source);
}

function handleGo(path: string) {
  router.push(path);
}
function handleJump(path: string) {
  window.location.href = path;
}

function handleVerification(): Promise<any> {
  if (!props.showCaptcha || props.captchaType !== 'slider') {
    return Promise.resolve(true);
  }
  return new Promise((resolve) => {
    captchaResolve = resolve;
    // captchaReject = reject;
    // 随机选择一个验证码类型
    const randomIndex = Math.floor(Math.random() * captchaChallengeTypeList.length);
    const selectedType = captchaChallengeTypeList[randomIndex];
    if (selectedType) {
      captchaChallengeType.value = selectedType;
    }
    verifyRef.value?.show();
  });
}
function handleError(error: any) {
  console.error(error);
  // if (captchaReject) {
  //   captchaReject(error);
  // }
}
function handleSuccess(data: any) {
  if (captchaResolve) {
    captchaResolve(data);
  }
}
const img = ref('');
const imgCaptchaKey = ref('');
const imgCaptchaCode = ref('');
const imgCaptchaError = ref(false);
async function getImgCaptcha() {
  if (props.imageCaptchaApi) {
    const res = await props.imageCaptchaApi();
    img.value = res.image;
    imgCaptchaKey.value = res.key;
    imgCaptchaCode.value = '';
    imgCaptchaError.value = false;
  }
}

// 监听验证码输入，清除错误状态
function handleCaptchaInput() {
  if (imgCaptchaCode.value && imgCaptchaError.value) {
    imgCaptchaError.value = false;
  }
}
if (props.showCaptcha && props.captchaType === 'pic') {
  getImgCaptcha();
}

onMounted(() => {
  if (localUsername) {
    formApi.setFieldValue('username', localUsername);
  }
});

defineExpose({
  getFormApi: () => formApi,
});
</script>

<template>
  <div @keydown.enter.prevent="handleSubmit">
    <slot name="title">
      <Title>
        <slot name="title">
          {{ title || `${$t('authentication.welcomeBack')} 👋🏻` }}
        </slot>
        <template #desc>
          <span class="text-muted-foreground">
            <slot name="subTitle">
              {{ subTitle || $t('authentication.loginSubtitle') }}
            </slot>
          </span>
        </template>
      </Title>
    </slot>

    <Form />

    <!-- 图形验证码 -->
    <div v-if="showCaptcha && captchaType === 'pic'" class="relative mb-6">
      <div class="flex items-center space-x-2">
        <div class="flex-1">
          <input
            v-model="imgCaptchaCode"
            type="text"
            :placeholder="$t('authentication.captchaPlaceholder')"
            class="ring-offset-background placeholder:text-muted-foreground w-full rounded-md border px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            :class="[
              imgCaptchaError
                ? 'border-red-500 bg-red-50 focus-visible:ring-red-500'
                : 'border-input bg-background focus-visible:ring-ring',
            ]"
            autocomplete="off"
            @input="handleCaptchaInput"
          />
        </div>
        <div class="flex-shrink-0">
          <img
            v-if="img"
            :src="img"
            alt="验证码"
            class="h-10 w-24 cursor-pointer rounded border"
            @click="getImgCaptcha"
            title="点击刷新验证码"
          />
          <div
            v-else
            class="flex h-10 w-24 cursor-pointer items-center justify-center rounded border bg-gray-100 text-xs text-gray-500"
            @click="getImgCaptcha"
          >
            点击获取
          </div>
        </div>
      </div>
      <div v-if="imgCaptchaError" class="absolute left-0 top-full mt-1 text-xs text-red-500">
        {{ $t('authentication.captchaTip') }}
      </div>
    </div>

    <div v-if="showRememberMe || showForgetPassword" class="mb-6 flex justify-between">
      <div class="flex-center">
        <VbenCheckbox v-if="showRememberMe" v-model:checked="rememberMe" name="rememberMe">
          {{ $t('authentication.rememberMe') }}
        </VbenCheckbox>
      </div>

      <span v-if="showForgetPassword" class="vben-link text-sm font-normal" @click="handleGo(forgetPasswordPath)">
        {{ $t('authentication.forgetPassword') }}
      </span>
    </div>
    <VbenButton
      :class="{
        'cursor-wait': loading,
      }"
      :loading="loading"
      aria-label="login"
      class="w-full"
      @click="handleSubmit"
    >
      {{ submitButtonText || $t('common.login') }}
    </VbenButton>

    <div v-if="showCodeLogin || showQrcodeLogin" class="mb-2 mt-4 flex items-center justify-between">
      <VbenButton v-if="showCodeLogin" class="w-1/2" variant="outline" @click="handleGo(codeLoginPath)">
        {{ $t('authentication.mobileLogin') }}
      </VbenButton>
      <VbenButton v-if="showQrcodeLogin" class="ml-4 w-1/2" variant="outline" @click="handleGo(qrCodeLoginPath)">
        {{ $t('authentication.qrcodeLogin') }}
      </VbenButton>
      <VbenButton v-if="showSsoLogin" class="ml-4 w-1/2" variant="outline" @click="handleJump(ssoLoginPath)">
        {{ $t('authentication.ssoLogin') }}
      </VbenButton>
    </div>

    <!-- 第三方登录 -->
    <slot name="third-party-login">
      <ThirdPartyLogin v-if="showThirdPartyLogin" @login="handleThirdPartyLogin" />
    </slot>

    <slot name="to-register">
      <div v-if="showRegister" class="mt-3 text-center text-sm">
        {{ $t('authentication.accountTip') }}
        <span class="vben-link text-sm font-normal" @click="handleGo(registerPath)">
          {{ $t('authentication.createAccount') }}
        </span>
      </div>
    </slot>
    <Verify
      mode="pop"
      :captcha-type="captchaChallengeType"
      :req-check="behaviorCaptchaCheckApi"
      :req-get="behaviorCaptchaApi"
      ref="verifyRef"
      @success="handleSuccess"
      @error="handleError"
    />
  </div>
</template>
