import { ref } from 'vue';

import { defineStore } from 'pinia';

export interface DictInfo {
  code: string;
  dictColor?: string;
  dictIcon?: string;
  dictName: string;
  dictValue: number | string;
  isTag?: number;
  sortCode: number;
}
export interface DictInfoExtended extends DictInfo {
  label?: string;
  value?: number | string;
}

const localDictList: DictInfoExtended[] = [
  { code: 'baseEnableType', dictColor: 'success', dictName: '启用', dictValue: 1, isTag: 1, sortCode: 0 },
  { code: 'baseEnableType', dictColor: 'error', dictName: '禁用', dictValue: 0, isTag: 1, sortCode: 0 },
  { code: 'baseBooleanType', dictColor: 'success', dictName: '是', dictValue: 1, isTag: 1, sortCode: 0 },
  { code: 'baseBooleanType', dictColor: 'error', dictName: '否', dictValue: 0, isTag: 1, sortCode: 0 },
  { code: 'dictType', dictName: '系统字典', dictValue: 'SYS', sortCode: 0 },
  { code: 'dictType', dictName: '业务字典', dictValue: 'BIZ', sortCode: 0 },
  { code: 'orgType', dictName: '企业', dictValue: 'COMPANY', sortCode: 0 },
  { code: 'orgType', dictName: '部门', dictValue: 'ORGAN', sortCode: 0 },
  { code: 'userStatus', dictColor: 'success', dictName: '启用', dictValue: 1, isTag: 1, sortCode: 0 },
  { code: 'userStatus', dictColor: 'warning', dictName: '锁定', dictValue: 0, isTag: 1, sortCode: 0 },
  { code: 'userStatus', dictColor: 'error', dictName: '禁用', dictValue: -1, isTag: 1, sortCode: 0 },
  { code: 'genderType', dictName: '保密', dictValue: 'SECRET', sortCode: 0 },
  { code: 'genderType', dictName: '男', dictValue: 'MALE', sortCode: 0 },
  { code: 'genderType', dictName: '女', dictValue: 'FEMALE', sortCode: 0 },
  { code: 'dictEditType', dictColor: 'success', dictName: '完全允许', dictValue: 'ALL', isTag: 1, sortCode: 0 },
  { code: 'dictEditType', dictColor: 'gold', dictName: '修改状态', dictValue: 'STATUS', isTag: 1, sortCode: 0 },
  { code: 'dictEditType', dictColor: 'gold', dictName: '修改名称', dictValue: 'NAME', isTag: 1, sortCode: 0 },
  { code: 'dictEditType', dictColor: 'error', dictName: '不可修改', dictValue: 'NONE', isTag: 1, sortCode: 0 },
  { code: 'formType', dictName: '输入框', dictValue: 'input', sortCode: 1 },
  { code: 'formType', dictName: '下拉框', dictValue: 'select', sortCode: 2 },
  { code: 'formType', dictName: '复选框', dictValue: 'check_box', sortCode: 3 },
  { code: 'formType', dictName: '单选框', dictValue: 'radio', sortCode: 4 },
  { code: 'categoryOperationType', dictName: '批量设置所有属性', dictValue: '1', sortCode: 1 },
  { code: 'categoryOperationType', dictName: '批量删除基础属性', dictValue: '2', sortCode: 1 },
  { code: 'categoryOperationType', dictName: '批量新增基础属性', dictValue: '3', sortCode: 1 },
  { code: 'systemSocialType', dictName: '企业微信', dictValue: 'wechat_enterprise', sortCode: 1 },
  { code: 'systemSocialType', dictName: '钉钉', dictValue: 20, sortCode: 2 },
  { code: 'serviceFeeType', dictName: '固定', dictValue: 1, sortCode: 1 },
  { code: 'serviceFeeType', dictName: '阶梯', dictValue: 2, sortCode: 2 },
];

export const useDictStore = defineStore(
  'dict',
  () => {
    const dictList = ref<DictInfo[]>([]);
    function setDict(dictRes: DictInfo[]) {
      dictList.value = [...dictRes, ...localDictList];
    }
    function getDictList(code: string): DictInfoExtended[] {
      return dictList.value
        .filter((item) => item.code === code)
        .map((item: DictInfoExtended) => ({
          ...item,
          label: item.dictName,
          value: item.dictValue,
        }))
        .sort((a, b) => (a.sortCode || 0) - (b.sortCode || 0));
    }

    function formatter(
      dictValue: number | string | unknown,
      code: string,
      _option?: { dictValueType?: 'number' | 'string' },
    ) {
      if (dictValue === null || dictValue === undefined || dictValue === '') {
        return dictValue;
      }
      const dictList: DictInfoExtended[] = getDictList(code);
      return dictList.find((item) => String(item.dictValue) === String(dictValue))?.dictName || dictValue;
    }

    function dictItemInfo(dictValue: number | string, code: string, _option?: { dictValueType?: 'number' | 'string' }) {
      if (dictValue === null || dictValue === undefined || dictValue === '') {
        return undefined;
      }
      const dictList: DictInfoExtended[] = getDictList(code);
      return dictList.find((item) => String(item.dictValue) === String(dictValue));
    }
    function $reset() {}
    return { $reset, dictItemInfo, dictList, formatter, getDictList, setDict };
  },
  {
    persist: {
      // 持久化
      pick: ['dictList'],
    },
  },
);
