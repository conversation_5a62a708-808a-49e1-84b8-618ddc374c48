import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/bpm',
    name: 'bpm',
    meta: {
      title: '工作流程',
      // hideInMenu: true,
    },
    children: [
      {
        path: 'manager',
        name: '<PERSON><PERSON><PERSON><PERSON>ger',
        meta: {
          title: '流程管理',
          icon: 'fa:dedent',
        },
        children: [
          {
            path: 'category',
            name: 'BpmCategory',
            component: () => import('#/views/bpm/category/index.vue'),
            meta: {
              title: '流程分类',
              icon: 'fa:object-ungroup',
            },
          },
          {
            path: 'model',
            name: 'BpmModel',
            component: () => import('#/views/bpm/model/index.vue'),
            meta: {
              title: '流程模型',
              icon: 'fa-solid:project-diagram',
            },
          },
          {
            path: 'form',
            name: 'BpmForm',
            component: () => import('#/views/bpm/form/index.vue'),
            meta: {
              title: '流程表单',
              icon: 'fa:hdd-o',
            },
          },
          {
            path: '/bpm/manager/form/edit',
            name: 'BpmFormEditor',
            component: () => import('#/views/bpm/form/designer/index.vue'),
            meta: {
              title: '编辑流程表单',
              activePath: '/bpm/manager/form',
              hideInMenu: true,
            },
            props: (route) => {
              return {
                id: route.query.id,
                type: route.query.type,
                copyId: route.query.copyId,
              };
            },
          },
          {
            path: 'process-instance/manager',
            name: 'BpmProcessInstanceManager',
            component: () => import('#/views/bpm/processInstance/manager/index.vue'),
            meta: {
              title: '流程实例',
              icon: 'fa:square',
            },
          },
          {
            path: 'process-task',
            name: 'BpmManagerTask',
            component: () => import('#/views/bpm/task/manager/index.vue'),
            meta: {
              title: '流程任务',
              icon: 'ep:collection-tag',
            },
          },
        ],
      },
      {
        path: 'task',
        name: 'BpmTask',
        meta: {
          title: '审批中心',
          icon: 'fa:tasks',
        },
        children: [
          {
            path: 'create',
            name: 'BpmProcessInstanceCreate',
            component: () => import('#/views/bpm/processInstance/create/index.vue'),
            meta: {
              title: '发起流程',
              icon: 'fa-solid:grin-stars',
            },
          },
          {
            path: 'my',
            name: 'BpmProcessInstanceMy',
            component: () => import('#/views/bpm/processInstance/index.vue'),
            meta: {
              title: '我的流程',
              icon: 'fa-solid:book',
            },
          },
          {
            path: 'todo',
            name: 'BpmTodoTask',
            component: () => import('#/views/bpm/task/todo/index.vue'),
            meta: {
              title: '待办任务',
              icon: 'fa:slack',
            },
          },
          {
            path: 'done',
            name: 'BpmDoneTask',
            component: () => import('#/views/bpm/task/done/index.vue'),
            meta: {
              title: '已办任务',
              icon: 'fa:delicious',
            },
          },
          {
            path: 'copy',
            name: 'BpmProcessInstanceCopy',
            component: () => import('#/views/bpm/task/copy/index.vue'),
            meta: {
              title: '抄送任务',
              icon: 'ep:copy-document',
            },
          },
        ],
      },
      {
        path: 'process-instance/detail',
        component: () => import('#/views/bpm/processInstance/detail/index.vue'),
        name: 'BpmProcessInstanceDetail',
        meta: {
          title: '流程详情',
          activePath: '/bpm/task/my',
          icon: 'ant-design:history-outlined',
          keepAlive: false,
          hideInMenu: true,
        },
        props: (route) => {
          return {
            id: route.query.id,
            taskId: route.query.taskId,
            activityId: route.query.activityId,
          };
        },
      },
      {
        path: 'manager/model/create',
        component: () => import('#/views/bpm/model/form/index.vue'),
        name: 'BpmModelCreate',
        meta: {
          title: '创建流程',
          activePath: '/bpm/manager/model',
          icon: 'carbon:flow-connection',
          hideInMenu: true,
          keepAlive: true,
        },
      },
      {
        path: 'manager/model/:type/:id',
        component: () => import('#/views/bpm/model/form/index.vue'),
        name: 'BpmModelUpdate',
        meta: {
          title: '修改流程',
          activePath: '/bpm/manager/model',
          icon: 'carbon:flow-connection',
          hideInMenu: true,
          keepAlive: true,
        },
      },
      {
        path: 'manager/definition',
        component: () => import('#/views/bpm/model/definition/index.vue'),
        name: 'BpmProcessDefinition',
        meta: {
          title: '流程定义',
          activePath: '/bpm/manager/model',
          icon: 'carbon:flow-modeler',
          hideInMenu: true,
          keepAlive: true,
        },
      },
      {
        path: 'process-instance/report',
        component: () => import('#/views/bpm/processInstance/report/index.vue'),
        name: 'BpmProcessInstanceReport',
        meta: {
          title: '数据报表',
          activeMenu: '/bpm/manager/model',
          icon: 'carbon:data-2',
          hideInMenu: true,
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
