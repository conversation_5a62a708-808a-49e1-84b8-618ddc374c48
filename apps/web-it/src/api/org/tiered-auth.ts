import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

// 组织权限项
export interface OrganizeAdminListItem {
  // 用户主键
  id?: number;
  // 机构主键
  organId?: number;
  // 机构类型
  organType?: string;
  // 本层添加
  thisLayerAdd?: number;
  // 本层编辑
  thisLayerEdit?: number;
  // 本层删除
  thisLayerDelete?: number;
  // 子层添加
  subLayerAdd?: number;
  // 子层编辑
  subLayerEdit?: number;
  // 子层删除
  subLayerDelete?: number;
  // 本层查看
  thisLayerSelect?: number;
  // 子层查看
  subLayerSelect?: number;
}

// 组织权限
export interface OrganizeAdminInfo {
  // 用户主键
  id?: string;
  // 组织权限
  organAdminList?: OrganizeAdminListItem[];
}

export const getTieredAuthPageApi = (params: PageListParams) => {
  return requestClient.get('/upms/permission/organ/admin/page', { params });
};
export const saveTieredAuthApi = (data: OrganizeAdminInfo) => {
  return requestClient.post('/upms/permission/organ/admin/save', data);
};
