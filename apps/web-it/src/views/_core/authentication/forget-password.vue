<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';
import type { Recordable } from '@vben/types';

import { computed, h, onBeforeUnmount, ref } from 'vue';
import { useRouter } from 'vue-router';

import { AuthenticationForgetPassword, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { VbenButton } from '@vben-core/shadcn-ui';

import { message } from 'ant-design-vue';

import {
  getEmailByUserNameApi,
  getMobileByUserNameApi,
  resetPasswordApi,
  sendEmailCodeApi,
  sendSmsCodeApi,
  validateCodeApi,
  validatePasswordApi,
} from '#/api';

defineOptions({ name: 'ForgetPassword' });

const router = useRouter();
const loading = ref(false);
const active = ref<1 | 2 | 3>(1);
interface StepConfig {
  [key: number]: VbenFormSchema[]; // 明确表示可以用数字索引
}
const confirmAccount = ref('');
const findType = ref('');
const userName = ref('');
const captcha = ref('');

// 发送验证码相关状态
const sendCodeLoading = ref(false);
const countdown = ref(0);
const countdownTimer = ref<NodeJS.Timeout | null>(null);

// 新密码远程校验相关状态
const passwordValidationError = ref<string>('');
const isPasswordValidating = ref(false);
const formSchema = computed((): VbenFormSchema[] => {
  // 确保响应 passwordValidationError 的变化
  const currentPasswordError = passwordValidationError.value;
  const step: StepConfig = {
    1: [
      {
        component: 'VbenInput',
        componentProps: {
          placeholder: '请输入找回账号',
        },
        fieldName: 'userName',
        label: $t('authentication.username'),
        rules: z.string().min(1, { message: $t('authentication.userNameTip') }),
      },
      {
        component: 'VbenSelect',
        componentProps: {
          placeholder: '请选择找回方式',
          options: [
            { label: '通过邮箱找回', value: 'email' },
            { label: '通过手机找回', value: 'mobile' },
          ],
        },
        fieldName: 'type',
        label: '找回方式',
        rules: z.string().min(1, { message: '请选择找回方式' }),
      },
    ],
    2: [
      {
        component: 'VbenInput',
        componentProps: {
          disabled: true,
          modelValue: confirmAccount.value,
        },
        fieldName: 'showAccount',
        label: '确认账号',
      },
      {
        component: 'VbenInput',
        componentProps: {
          placeholder: '请确认邮箱/手机号',
        },
        fieldName: 'confirmAccount',
        label: '确认账号',
        rules: z.string({ required_error: '请输入确认邮箱/手机号' }).min(1, { message: '请输入确认邮箱/手机号' }),
      },
      {
        component: 'VbenInput',
        componentProps: {
          placeholder: '请输入验证码',
          addonAfter: () =>
            h(
              VbenButton,
              {
                disabled: sendCodeLoading.value || countdown.value > 0,
                loading: sendCodeLoading.value,
                class: 'h-10 w-20',
                variant: 'default',
                onClick: handleSendCode,
              },
              () => (countdown.value > 0 ? `${countdown.value}s` : '发送验证码'),
            ),
        },
        fieldName: 'captcha',
        label: '验证码',
        rules: z.string({ required_error: '请输入验证码' }).min(1, { message: '请输入验证码' }),
      },
    ],
    3: [
      {
        component: 'VbenInputPassword',
        componentProps: {
          placeholder: '请输入新密码',
          onBlur: async (e: Event) => {
            const target = e.target as HTMLInputElement;
            if (target?.value) {
              await validatePassword(target.value);
              // 手动触发表单验证以显示错误信息
              const formApi = AuthForgetPasswordRef.value?.getFormApi();
              if (formApi) {
                await formApi.validateField('newPassword');
              }
            }
          },
        },
        fieldName: 'newPassword',
        label: '新密码',
        rules: z
          .string({ required_error: '请输入新密码' })
          .min(1, { message: '请输入新密码' })
          .refine(
            (value) => {
              // 只在有密码验证错误时返回false，否则返回true
              if (!value || !userName.value) return true;
              return !passwordValidationError.value;
            },
            {
              message: currentPasswordError || '密码强度验证失败',
            },
          ),
      },
      {
        component: 'VbenInputPassword',
        componentProps: {
          placeholder: '请确认新密码',
          type: 'password',
        },
        dependencies: {
          rules(values) {
            const { newPassword } = values;
            return z
              .string({ required_error: '请输入确认新密码' })
              .min(1, { message: '请输入确认新密码' })
              .refine((value) => value === newPassword, {
                message: '两次输入的密码不一致',
              });
          },
          triggerFields: ['newPassword'],
        },
        fieldName: 'confirmPassword',
        label: '确认新密码',
      },
    ],
  };
  return step[active.value]!;
});
// 发送验证码函数
async function handleSendCode() {
  const formApi = AuthForgetPasswordRef.value?.getFormApi();
  // 前置校验
  if (!userName.value) {
    console.error('用户名未填写');
    return;
  }

  if (!findType.value) {
    console.error('找回方式未选择');
    return;
  }

  const res = await formApi.validateField('confirmAccount');
  if (!res.valid) {
    return;
  }
  try {
    sendCodeLoading.value = true;
    const values = await formApi.getValues();
    // 根据找回方式选择对应的API
    await (findType.value === 'mobile'
      ? sendSmsCodeApi({
          mobile: values.confirmAccount,
          userName: userName.value,
        })
      : sendEmailCodeApi({
          email: values.confirmAccount,
          userName: userName.value,
        }));

    // 开始倒计时
    startCountdown();
  } catch (error) {
    console.error('发送验证码失败:', error);
  } finally {
    sendCodeLoading.value = false;
  }
}

// 开始倒计时
function startCountdown() {
  countdown.value = 60;
  countdownTimer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value!);
      countdownTimer.value = null;
    }
  }, 1000);
}

// 清理定时器
function clearCountdownTimer() {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
    countdownTimer.value = null;
    countdown.value = 0;
  }
}

const AuthForgetPasswordRef = ref();
async function handleSubmit(value: Recordable<any>) {
  switch (active.value) {
    case 1: {
      userName.value = value.userName;
      findType.value = value.type;
      let api = getMobileByUserNameApi;
      if (value.type === 'email') {
        // 通过邮箱找回
        api = getEmailByUserNameApi;
      }
      confirmAccount.value = await api({ userName: value.userName });
      active.value = 2;

      break;
    }
    case 2: {
      await validateCodeApi({ captcha: value.captcha, userName: userName.value });
      captcha.value = value.captcha;
      active.value = 3;
      break;
    }
    case 3: {
      const params = {
        captcha: captcha.value,
        newPassword: value.newPassword,
        userName: userName.value,
      };
      await resetPasswordApi(params);
      message.success('密码重置成功，请重新登录');
      await router.push({ name: 'Login' });
      break;
    }
    // No default
  }
}

// 新密码远程校验函数
async function validatePassword(password: string) {
  if (!password || !userName.value) {
    passwordValidationError.value = '';
    return true;
  }

  try {
    isPasswordValidating.value = true;
    const res = await validatePasswordApi({
      password,
      userName: userName.value,
    });

    if (res.code !== 200) {
      passwordValidationError.value = res.msg || '密码强度验证失败';
      return false;
    }

    passwordValidationError.value = '';
    return true;
  } catch (error) {
    console.error('密码验证失败:', error);
    passwordValidationError.value = '密码验证失败，请重试';
    return false;
  } finally {
    isPasswordValidating.value = false;
  }
}

// 组件卸载时清理定时器
onBeforeUnmount(() => {
  clearCountdownTimer();
});
</script>

<template>
  <AuthenticationForgetPassword
    ref="AuthForgetPasswordRef"
    :form-schema="formSchema"
    :loading="loading"
    @submit="handleSubmit"
  />
</template>
