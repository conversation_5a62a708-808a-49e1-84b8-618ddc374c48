<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { GroupInfo, UserInfo } from '#/api';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Button, Dropdown, Menu, MenuItem, message, Space, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteGroupApi, getUserPageListApi } from '#/api';
import { saveTieredAuthApi } from '#/api/org/tiered-auth';
import Auth from '#/views/org/tiered-auth/auth.vue';
import EditTieredAuthForm from '#/views/org/tiered-auth/components/edit-tiered-auth-form.vue';

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'code',
      label: '工号',
    },
    {
      component: 'Input',
      fieldName: 'account',
      label: '账号',
    },
    {
      component: 'Input',
      fieldName: 'realName',
      label: '姓名',
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'seq', type: 'seq', width: 80 },
    { field: 'code', title: '工号' },
    { field: 'account', title: '账号' },
    { field: 'realName', title: '姓名' },
    { field: 'organFullNames', title: '所属组织' },
    { field: 'createTime', title: '创建时间', formatter: 'formatDateTime' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getUserPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const edit = (row: GroupInfo) => {
  modalTitle.value = $t('base.edit');
  modalApi.setData(row).open();
};
const editTieredAuthFormRef = ref();
const loading = ref({
  save: false,
});
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    const formInfo = await editTieredAuthFormRef.value.onSubmit();
    try {
      loading.value.save = true;
      await saveTieredAuthApi(formInfo);
      message.success($t('base.resSuccess'));
      await modalApi.close();
      await gridApi.reload();
    } finally {
      loading.value.save = false;
    }
  },
  onOpened() {
    const value = modalApi.getData<Record<string, any>>();
    editTieredAuthFormRef.value.init(value);
  },
});
const modalTitle = ref($t('base.add'));
const addRole = () => {
  modalTitle.value = $t('base.add');
  modalApi.setData(null).open();
};
const del = async (row: GroupInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await deleteGroupApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const [registerAuthForm, { openPopup: openAuthFormPopup }] = usePopup();
const editAuth = (row: UserInfo) => {
  openAuthFormPopup(true, row);
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="addRole">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <Dropdown>
            <TypographyLink>
              <Space :size="0">
                {{ $t('base.more') }}
                <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
              </Space>
            </TypographyLink>
            <template #overlay>
              <Menu>
                <MenuItem key="auth" @click="editAuth(row)"> 分级授权 </MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </Space>
      </template>
    </Grid>
    <Modal :title="modalTitle" :confirm-loading="loading.save" class="w-[1000px]">
      <EditTieredAuthForm ref="editTieredAuthFormRef" />
    </Modal>
    <Auth @register="registerAuthForm" />
  </Page>
</template>

<style></style>
