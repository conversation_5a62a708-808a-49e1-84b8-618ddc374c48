import type { SchemaItem } from '../index';

export const name: string = '限制招投标被执行人';
export const schema: SchemaItem[] = [
  { fieldName: 'ename', label: '被执行人' },
  { fieldName: 'identification_code', label: '被执行人证件号码' },
  { fieldName: 'address', label: '被执行人地址' },
  { fieldName: 'case_no', label: '案号' },
  { fieldName: 'doc_number', label: '执行依据文号' },
  { fieldName: 'enforcement_basis', label: '执行依据' },
  { fieldName: 'case_reason', label: '执行案由' },
  { fieldName: 'amount', label: '执行标的金额' },
  { fieldName: 'unexecuted_amount', label: '未执行金额' },
  { fieldName: 'court', label: '执行法院' },
  { fieldName: 'publish_date', label: '发布日期/曝光日期' },
  { fieldName: 'filing_date', label: '立案日期' },
  { fieldName: 'court_name', label: '标准法院名' },
  { fieldName: 'standard_case_no', label: '标准案号' },
];
