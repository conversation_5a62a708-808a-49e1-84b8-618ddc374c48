import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface DeliveryPageParams extends BaseDataParams {
  /** 当前页 */
  current?: string;
  /** 每页的数量 */
  size?: string;
  /** 正排序规则 */
  ascs?: string;
  /** 倒排序规则 */
  descs?: string;
  /** 委托提货函编号 */
  docCode?: string;
  /** 所属项目ID */
  projectId?: string;
  /** 所属项目名称 */
  projectName?: string;
  /** 所属项目编号 */
  projectCode?: string;
  /** 受托企业编码 */
  entrustedCompanyCode?: string;
  /** 受托企业名称 */
  entrustedCompanyName?: string;
  /** 委托日期 */
  entrustedDate?: string;
  /** 业务状态 */
  status?: string;
  /** 审批状态 */
  approvalStatus?: string;
  /** 原始文件ID */
  originalFileId?: string;
  /** 盖章文件ID */
  signedFileId?: string;
  /** 经办人ID */
  userId?: string;
  /** 经办人姓名 */
  userName?: string;
  /** 申请部门ID */
  organId?: string;
  /** 申请部门名称 */
  organName?: string;
  /** 备注说明 */
  description?: string;
  /** 版本号 */
  version?: string;
  /** 主键 */
  id?: string;
}

export interface DeliveryBaseInfo extends BaseDataParams {
  /** 主键 */
  id?: string;
  /** 委托提货函编号 */
  docCode?: string;
  /** 所属项目ID */
  projectId?: string;
  /** 所属项目名称 */
  projectName?: string;
  /** 所属项目编号 */
  projectCode?: string;
  /** 受托企业编码 */
  entrustedCompanyCode?: string;
  /** 受托企业名称 */
  entrustedCompanyName?: string;
  /** 委托日期 */
  entrustedDate?: string;
  /** 业务状态 */
  status?: string;
  /** 审批状态 */
  approvalStatus?: string;
  /** 原始文件ID */
  originalFileId?: string;
  /** 盖章文件ID */
  signedFileId?: string;
  /** 经办人ID */
  userId?: string;
  /** 经办人姓名 */
  userName?: string;
  /** 申请部门ID */
  organId?: string;
  /** 申请部门名称 */
  organName?: string;
  /** 备注说明 */
  description?: string;
  /** 版本号 */
  version?: string;
  /** 业务附件 */
  attachmentList?: string[];
}

// 分页查询
export async function getDeliveryPageApi(params: PageListParams) {
  return requestClient.get<DeliveryPageParams>('/scm/delivery/manage/page', { params });
}

// 新增
export async function addDeliveryApi(data: DeliveryBaseInfo) {
  return requestClient.post<DeliveryBaseInfo>('/scm/delivery/manage/add', data);
}

// 编辑
export async function editDeliveryApi(data: DeliveryBaseInfo) {
  return requestClient.post<DeliveryBaseInfo>('/scm/delivery/manage/edit', data);
}

// 提交
export async function submitDeliveryApi(data: DeliveryBaseInfo) {
  return requestClient.post<DeliveryBaseInfo>('/scm/delivery/manage/submit', data);
}

// 详情
export async function detailDeliveryApi(id: string) {
  return requestClient.get(`/scm/delivery/manage/detail/${id}`);
}

// 删除
export async function delDeliveryApi(id: string) {
  return requestClient.post(`/scm/delivery/manage/delete/${id}`);
}
