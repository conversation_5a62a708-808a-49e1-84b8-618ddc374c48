import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface TransferApplyInfo {
  id?: string;
  status?: string;
  // 审批状态
  approvalStatus?: string;
  // 进出库状态
  inoutType?: string;
  // 是否按SN码管理
  isSnManaged?: number;
  // 仓库名称
  warehouseName?: string;
  // 仓库ID
  warehouseId?: string;
  // 申请提货日期
  applyDate?: string;
  // 提货申请编号
  transferApplyCode?: string;
  projectId?: string;
  projectName?: string;
  projectCode?: string;
  // 客户企业(下游企业)
  customerCompanyCode?: string;
  customerCompanyName?: string;
  // 仓储企业名称
  warehouseCompanyName?: string;
  // 控货方/贸易执行企业
  executorCompanyName?: string;
  executorCompanyCode?: string;
  transferApplyItems?: any[];
  projectPartners?: any[];
  transferApplyItemRequests?: any[];
  remarks?: string;
  orderState?: string;
  sourceDocumentType?: string;
  totalTransferValue?: string;
  attachmentList?: string;
  businessStructure?: string;
}
export async function getTransferListApi(params: PageListParams) {
  return requestClient.get('/scm/transfer/apply/list', { params });
}
export async function infoTransferApi(params: TransferApplyInfo) {
  return requestClient.get('/scm/transfer/apply/detail', { params });
}
export async function delTransferApi(id: string) {
  return requestClient.post(`/scm/transfer/apply/delete?id=${id}`);
}
export async function changeTransferApi(params: TransferApplyInfo) {
  return requestClient.post('/scm/transfer/apply/change', params);
}
export async function addTransferApi(params: TransferApplyInfo) {
  return requestClient.post('/scm/transfer/apply/save', params);
}
export async function updateTransferApi(params: TransferApplyInfo) {
  return requestClient.post('/scm/transfer/apply/update', params);
}
// 出库商品查询
export async function getQueryItemListApi(params: TransferApplyInfo) {
  return requestClient.post('/scm/inventory/stock/queryItemList', params);
}
// 仓库信息条件查询
export async function getWarehouseListApi(params: TransferApplyInfo) {
  return requestClient.get('/scm/warehouse/manage/list', { params });
}
