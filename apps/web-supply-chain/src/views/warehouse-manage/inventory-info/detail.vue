<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { Warehouse_inventoryInfo } from '#/api';

import { ref, watch } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP, DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { infoInventoryApi } from '#/api';

defineEmits(['register']);
const productInfo = ref<Warehouse_inventoryInfo>({});

const init = async (data: Warehouse_inventoryInfo) => {
  if (data?.id) {
    productInfo.value = await infoInventoryApi({ id: data.id });
  }
};
const [registerPopup] = usePopupInner(init);

const bankGridOptions = {
  footerMethod() {
    const footerRow = {
      total: '合计',
      beginQuantity: productInfo.value.totalInboundQuantity,
      lockedQuantity: productInfo.value.totalOutboundQuantity,
      availableQuantity: productInfo.value.totalOutboundQuantity,
    };
    return [footerRow];
  },
  columns: [
    { field: 'total' },
    { field: 'version', title: '入库单编号', minWidth: '160px' },
    { field: 'beginQuantity', title: '库存重量', minWidth: '160px' },
    { field: 'lockedQuantity', title: '锁定重量', minWidth: '160px' },
    { field: 'availableQuantity', title: '可用重量', minWidth: '160px' },
    { field: 'serialNumber', title: 'SN码', minWidth: '160px' },
    { field: 'remarks', title: '入库备注', minWidth: '160px' },
  ],
  ...DETAIL_GRID_OPTIONS,
  showFooter: true,
} as VxeTableGridOptions;
const [ProducGrid, bankGridApi] = useVbenVxeGrid({
  gridOptions: bankGridOptions,
});
watch(
  () => productInfo.value,
  (val = {}) => {
    bankGridApi.grid.reloadData(val.inventoryStockSerialList ?? []);
  },
  { deep: true },
);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="订单信息" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="商品名称">
          {{ productInfo.productName }}
        </a-descriptions-item>
        <a-descriptions-item label="商品别名">
          {{ productInfo.productAlias }}
        </a-descriptions-item>
        <a-descriptions-item label="仓库名称">
          {{ productInfo.warehouseName }}
        </a-descriptions-item>
        <a-descriptions-item label="仓储运营方">
          {{ productInfo.warehouseName }}
        </a-descriptions-item>
        <a-descriptions-item label="项目名称">
          {{ productInfo.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="采购订单名称">
          {{ productInfo.purchaseOrderName }}
        </a-descriptions-item>
        <a-descriptions-item label="销售订单名称">
          {{ productInfo.salesOrderName }}
        </a-descriptions-item>
        <a-descriptions-item label="下游企业">
          {{ productInfo.purchaserName }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="商品信息" />
      <ProducGrid />
    </div>
  </BasicPopup>
</template>

<style></style>
