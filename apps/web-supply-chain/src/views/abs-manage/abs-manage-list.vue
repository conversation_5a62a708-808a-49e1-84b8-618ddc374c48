<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { ABSManageInfo } from '#/api/abs-manage';

import { useModalUrl } from '@vben/base-ui';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { message, TypographyLink } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { cancelABSApi, deleteABSApi, getABSListApi } from '#/api/abs-manage';

import Detail from './abs-manage-detail.vue';
import Form from './abs-manage-form.vue';

const dictStore = useDictStore();

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'absProjectCode',
      label: 'ABS项目编号',
    },
    {
      component: 'Input',
      fieldName: 'absProjectName',
      label: 'ABS项目名称',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('REVIEW_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '业务状态',
      componentProps: {
        options: dictStore.getDictList('BUS_STATUS'),
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'projectDate',
      label: '立项日期',
    },
  ],
  fieldMappingTime: [
    ['projectDate', ['projectStartDate', 'projectEndDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
  ],
  showCollapseButton: true,
  submitOnEnter: true,
  wrapperClass: 'grid-cols-1 md:grid-cols-3',
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'absProjectCode', title: '编号' },
    {
      field: 'absProjectName',
      title: 'ABS项目名称',
    },
    {
      field: 'projectName',
      title: '项目名称',
    },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'BUS_STATUS',
        },
      },
    },
    {
      field: 'approvalStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    { field: 'createTime', title: '创建时间' },
    { field: 'createName', title: '创建人' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getABSListApi({
          current: page.currentPage,
          size: page.pageSize,
          descs: 'create_time',
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const add = () => {
  openFormPopup(true, { pageType: 'edit' });
};
const edit = (row: ABSManageInfo) => {
  openFormPopup(true, { ...row, pageType: 'edit' });
};
const detail = (row: ABSManageInfo) => {
  openDetailPopup(true, { ...row, pageType: 'detail' });
};
const invalidate = (row: ABSManageInfo, type: string) => {
  AntdModal.confirm({
    title: `确认${type}`,
    content: `此操作将${type}该数据，是否继续？`,
    async onOk() {
      await cancelABSApi(row.id || '');
      message.success($t('base.resSuccess'));
      await gridApi.formApi.submitForm();
    },
  });
};
const editSuccess = () => {
  gridApi.formApi.submitForm();
};
const del = async (row: ABSManageInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await deleteABSApi(row.id || '');
      message.success($t('base.resSuccess'));
      await gridApi.formApi.submitForm();
    },
  });
};

const access = (row: { id: number }) => {
  openFormPopup(true, { ...row, pageType: 'edit' });
};
const viewDetail = (row: { id: number }) => {
  openDetailPopup(true, { ...row, pageType: 'detail' });
};
const audit = (row: { id: number }) => {
  openDetailPopup(true, { ...row, pageType: 'audit' });
};

useModalUrl({
  handlers: {
    // 编辑弹层
    edit: (params) => {
      const data: AccessInfo = {
        id: params.id,
      };
      access(data);
    },

    // 详情弹层
    detail: (params) => {
      const data: AccessInfo = {
        id: params.id,
      };
      viewDetail(data);
    },

    // 审核弹层
    audit: (params) => {
      const data: AccessInfo = {
        id: params.id,
        pageType: 'audit',
      };
      audit(data);
    },
  },
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.Add') }}
        </a-button>
      </template>
      <template #action="{ row }">
        <a-space>
          <!-- 待提交才能编辑 已拒绝的也可重新发起-->
          <TypographyLink
            @click="edit(row)"
            v-if="row.status === 'DRAFTING' || row.approvalStatus === 'REJECTED'"
            v-show="!(row.approvalStatus === 'REJECTED' && row.status === 'CANCELLED')"
          >
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <!-- 待提交才能删除 -->
          <TypographyLink type="danger" @click="del(row)" v-if="row.status === 'DRAFTING'">
            {{ $t('base.del') }}
          </TypographyLink>
          <!-- 只有审批拒绝才能作废 -->
          <TypographyLink
            type="danger"
            @click="invalidate(row, '作废')"
            v-if="row.approvalStatus === 'REJECTED'"
            v-show="!(row.approvalStatus === 'REJECTED' && row.status === 'CANCELLED')"
          >
            {{ $t('base.invalidate') }}
          </TypographyLink>
        </a-space>
      </template>
    </Grid>
    <Form @register="registerForm" @ok="editSuccess" />
    <Detail @register="registerDetail" @ok="editSuccess" />
  </Page>
</template>

<style></style>
