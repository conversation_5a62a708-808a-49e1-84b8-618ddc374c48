<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { Modal, Segmented } from 'ant-design-vue';

const emit = defineEmits(['confirm']);

const radioStyle = reactive({
  display: 'flex',
  height: '50px',
  lineHeight: '50px',
});
const segmentedValue = ref('');
const radioType = ref<number>(1);
const visible = ref(false);
// 是否含税单价
const useTaxPriceCalc = ref(false);

// 为每个输入框维护独立的值
const fixedValue = ref<number | string>('');
const percentageValue = ref<number | string>('');
const floatValue = ref<number | string>('');

const segmentedData = computed(() =>
  useTaxPriceCalc.value ? ['调整不含税单价', '调整税率'] : ['调整含税单价', '调整税率'],
);

// 根据当前选中的选项和类型确定应该使用哪个值
const inputValue = computed(() => {
  if (radioType.value === 1) {
    return fixedValue.value;
  } else if (radioType.value === 2 && segmentedValue.value !== '调整税率') {
    return percentageValue.value;
  } else if (radioType.value === 3) {
    return floatValue.value;
  }
  return '';
});

const openModal = (data: boolean) => {
  useTaxPriceCalc.value = data;
  segmentedValue.value = segmentedData.value[0] || '';
  radioType.value = 1;
  // 清空所有值
  fixedValue.value = '';
  percentageValue.value = '';
  floatValue.value = '';
  visible.value = true;
};

const handleOk = () => {
  emit('confirm', {
    segmentedValue: segmentedValue.value,
    radioType: radioType.value,
    inputValue: inputValue.value,
  });
  visible.value = false;
};

// 为每个输入框创建独立的处理函数
const handleFixedValueChange = (value: number) => {
  if (radioType.value === 1) {
    // 只有当前选项启用时才更新
    fixedValue.value = value;
  }
};

const handlePercentageValueChange = (value: number) => {
  if (radioType.value === 2 && segmentedValue.value !== '调整税率') {
    percentageValue.value = value;
  }
};

const handleFloatValueChange = (value: number) => {
  if (radioType.value === 3) {
    floatValue.value = value;
  }
};

defineExpose({ openModal });
</script>

<template>
  <Modal v-model:visible="visible" title="批量设置" @ok="handleOk">
    <div>
      <div>
        <Segmented
          v-model:value="segmentedValue"
          :options="segmentedData"
          @change="
            () => {
              radioType = 1;
            }
          "
        />
      </div>
      <br />
      <a-radio-group
        v-model:value="radioType"
        @change="
          () => {
            // 切换选项时不清理值
          }
        "
      >
        <a-radio :value="1" :style="radioStyle">
          <div class="flex items-center">
            <span class="w-40">设为固定值</span>
            <a-input-number
              class="w-40"
              placeholder="请输入固定值"
              :disabled="radioType !== 1"
              :value="fixedValue"
              @input="handleFixedValueChange"
            />
          </div>
        </a-radio>
        <a-radio :value="2" :style="radioStyle" v-if="segmentedValue !== '调整税率'">
          <div class="flex items-center">
            <span class="w-40">基价上/下浮动比率(%)</span>
            <a-input-number
              class="w-40"
              placeholder="请输入比率"
              :disabled="radioType !== 2"
              :value="percentageValue"
              @input="handlePercentageValueChange"
            />
          </div>
        </a-radio>
        <a-radio :value="3" :style="radioStyle">
          <div class="flex items-center">
            <span class="w-40">基价上/下浮动值</span>
            <a-input-number
              class="w-40"
              placeholder="请输入值"
              :disabled="radioType !== 3"
              :value="floatValue"
              @input="handleFloatValueChange"
            />
          </div>
        </a-radio>
      </a-radio-group>
    </div>
  </Modal>
</template>
