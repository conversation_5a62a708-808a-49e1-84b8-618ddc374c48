<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';
import type { Recordable } from '@vben/types';

import { computed, ref } from 'vue';

import { AuthenticationCodeLogin, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { sm2 } from 'sm-crypto';

import { checkBehaviorCaptchaApi, getBehaviorCaptchaApi, sendLoginSmsCodeApi } from '#/api';
import { useAuthStore } from '#/store';

defineOptions({ name: 'CodeLogin' });

const authStore = useAuthStore();
const CODE_LENGTH = 6;

const AuthenticationCodeLoginRef = ref();
const smsId = ref('');
const sendSmsCode = async () => {
  const formApi = AuthenticationCodeLoginRef.value.getFormApi();
  const validRes = await formApi.validateField('phoneNumber');
  if (!validRes.valid) throw new Error(validRes.errors);
  let phoneNumber = validRes.value;
  if (import.meta.env.VITE_LOGIN_ENCRYPT === 'ON') {
    phoneNumber = sm2.doEncrypt(validRes.value, import.meta.env.VITE_ENCRYPT_PUBLIC_KEY, 0);
  }
  const captchaRes = await AuthenticationCodeLoginRef.value.handleVerification();
  smsId.value = await sendLoginSmsCodeApi(
    { phone: phoneNumber },
    {
      'Captcha-Code': captchaRes.captchaVerification,
    },
  );
};

const formSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: $t('authentication.mobile'),
      },
      fieldName: 'phoneNumber',
      label: $t('authentication.mobile'),
      rules: z
        .string()
        .min(1, { message: $t('authentication.mobileTip') })
        .refine((v) => /^\d{11}$/.test(v), {
          message: $t('authentication.mobileErrortip'),
        }),
    },
    {
      component: 'VbenPinInput',
      componentProps: {
        codeLength: CODE_LENGTH,
        createText: (countdown: number) => {
          const text = countdown > 0 ? $t('authentication.sendText', [countdown]) : $t('authentication.sendCode');
          return text;
        },
        placeholder: $t('authentication.code'),
        handleSendCode: sendSmsCode,
      },
      fieldName: 'code',
      label: $t('authentication.code'),
      rules: z.string().length(CODE_LENGTH, {
        message: $t('authentication.codeTip', [CODE_LENGTH]),
      }),
    },
  ];
});
/**
 * 异步处理登录操作
 * Asynchronously handle the login process
 * @param values 登录表单数据
 */
async function handleLogin(values: Recordable<any>) {
  const formData = {
    grant_type: 'sms_code',
    id: smsId.value,
    phone: values.phoneNumber,
    value: values.code,
  };
  await authStore.authLogin(formData);
}
</script>

<template>
  <AuthenticationCodeLogin
    ref="AuthenticationCodeLoginRef"
    :form-schema="formSchema"
    :loading="authStore.loginLoading"
    :behavior-captcha-api="getBehaviorCaptchaApi"
    :behavior-captcha-check-api="checkBehaviorCaptchaApi"
    @submit="handleLogin"
  />
</template>
