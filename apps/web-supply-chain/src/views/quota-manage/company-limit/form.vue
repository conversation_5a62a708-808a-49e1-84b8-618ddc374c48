<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { CompanylimitInfo } from '#/api/quota-manage';

import { computed, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';

import { message, Select } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { addCreditApi, detailCreditApi, getCompanyRecordApi, updateCreditApi } from '#/api/quota-manage';

const emit = defineEmits(['ok']);
const dictStore = useDictStore();

const formData = ref<CompanylimitInfo>({});
const colSpan = { md: 12, sm: 24 };
const rules: Record<string, Rule[]> = {
  creditCompanyName: [{ required: true, message: '请选择额度主体', trigger: 'blur' }],
  creditType: [{ required: true, message: '请选择授信类型', trigger: 'blur' }],
  creditLimit: [{ required: true, message: '请输入企业额度上限', trigger: 'blur' }],
  expiryDate: [{ required: true, message: '请选择额度到期日', trigger: 'blur' }],
};
const title = computed(() => {
  return formData.value.id ? '编辑企业额度' : '新增企业额度';
});
const init = async (data: CompanylimitInfo) => {
  if (data.id) {
    const obj = await detailCreditApi({ id: data.id });
    formData.value = { ...obj, creditType: String(obj.creditType) };
  } else {
    formData.value = data;
  }
};

const formRef = ref();
const save = async () => {
  await formRef.value.validate();
  const formDatas = cloneDeep(formData.value);
  formDatas.status = 'SUBMITTED';
  changeOkLoading(true);
  let api = addCreditApi;
  if (formData.value.id) {
    api = updateCreditApi;
  }
  try {
    const res = await api(formDatas);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <a-form
      ref="formRef"
      :colon="false"
      :model="formData"
      :rules="rules"
      :label-col="{ style: { width: '150px' } }"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <a-row class="mt-5">
        <a-col v-bind="colSpan">
          <a-form-item label="企业额度管理编号" name="creditCode">
            <a-input v-model:value="formData.creditCode" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="额度主体" name="creditCompanyName">
            <ApiComponent
              v-model="formData.creditCompanyName"
              :component="Select"
              :api="getCompanyRecordApi"
              label-field="companyName"
              value-field="companyName"
              model-prop-name="value"
              @change="
                (value: string, option: any) => {
                  formData.creditCompanyCode = option.companyCode;
                }
              "
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="统一社会信用代码" name="creditCompanyCode">
            <a-input v-model:value="formData.creditCompanyCode" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="授信类型" name="creditType">
            <a-select v-model:value="formData.creditType" :options="dictStore.getDictList('CREDIT_TYPE')" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="企业额度上限（元）" name="creditLimit">
            <a-input v-model:value="formData.creditLimit" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="额度到期日" name="expiryDate">
            <a-date-picker v-model:value="formData.expiryDate" value-format="YYYY-MM-DD HH:mm:ss" class="w-full" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="remarks">
            <a-textarea v-model:value="formData.remark" :rows="4" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </BasicPopup>
</template>
