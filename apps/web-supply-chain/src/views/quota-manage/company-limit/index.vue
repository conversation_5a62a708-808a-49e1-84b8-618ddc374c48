<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { CompanylimitInfo } from '#/api/quota-manage';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { message, TypographyLink } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteCreditApi, getCreditListApi } from '#/api/quota-manage';

import Detail from './detail.vue';
import Form from './form.vue';

const dictStore = useDictStore();
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'creditCode',
      label: '企业额度管理编号',
    },
    {
      component: 'Input',
      fieldName: 'creditCompanyName',
      label: '额度主体',
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '业务状态',
      componentProps: {
        options: dictStore.getDictList('BUS_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'creditType',
      label: '授信类型',
      componentProps: {
        options: dictStore.getDictList('CREDIT_TYPE'),
      },
    },
  ],
  showCollapseButton: true,
  submitOnEnter: true,
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'creditCode', title: '企业额度管理编号' },
    { field: 'creditCompanyName', title: '额度主体' },
    {
      field: 'creditLimit',
      title: '企业额度上限（元）',
    },
    {
      field: 'status',
      title: '业务状态',
      formatter: ['formatStatus', 'BUS_STATUS'],
    },
    { field: 'approvalStatus', title: '审批状态' },
    {
      field: 'creditType',
      title: '授信类型',
      formatter: ({ cellValue }) => {
        return dictStore.formatter(String(cellValue), 'CREDIT_TYPE') || cellValue;
      },
    },
    { field: 'expiryDate', title: '额度到期日' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getCreditListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const del = async (row: CompanylimitInfo) => {
  AntdModal.confirm({
    title: '确认删除',
    content: `此操作将删除该数据，是否继续？`,
    async onOk() {
      await deleteCreditApi(row.id as string);
      message.success($t('base.resSuccess'));
      await gridApi.formApi.submitForm();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-button class="mr-2" type="primary" @click="openFormPopup(true, {})">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.Add') }}
        </a-button>
      </template>
      <template #action="{ row }">
        <a-space>
          <TypographyLink @click="openFormPopup(true, row)"> 编辑 </TypographyLink>
          <TypographyLink @click="del(row)" type="danger"> 删除 </TypographyLink>
          <TypographyLink @click="openDetailPopup(true, row)"> 详情 </TypographyLink>
        </a-space>
      </template>
    </Grid>
    <Detail @register="registerDetail" />
    <Form @register="registerForm" @ok="gridApi.formApi.submitForm()" />
  </Page>
</template>

<style></style>
