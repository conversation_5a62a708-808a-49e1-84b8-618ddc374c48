<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getCompanyListApi } from '#/api/quota-manage';

import Detail from './detail.vue';

const dictStore = useDictStore();
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'limitCode',
      label: '额度记录编号',
    },
    {
      component: 'Input',
      fieldName: 'creditCompanyName',
      label: '额度主体',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '额度状态',
      componentProps: {
        options: dictStore.getDictList('BUS_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'creditType',
      label: '授信类型',
      componentProps: {
        options: dictStore.getDictList('CREDIT_TYPE'),
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'expiryDate',
      label: '额度到期日',
    },
  ],
  fieldMappingTime: [['expiryDate', ['beginDate', 'endDate']]],
  showCollapseButton: true,
  submitOnEnter: true,
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'limitCode', title: '额度记录编号' },
    { field: 'creditCompanyName', title: '额度主体' },
    {
      field: 'creditLimit',
      title: '企业额度上限（元）',
    },
    {
      field: 'usedLimit',
      title: '已用额度(元)',
    },
    {
      field: 'frozenLimit',
      title: '冻结额度（元）',
    },
    {
      field: 'availableLimit',
      title: '可用额度（元）',
    },
    {
      field: 'status',
      title: '额度状态',
      formatter: ['formatStatus', 'BUS_STATUS'],
    },
    {
      field: 'creditType',
      title: '授信类型',
      formatter: ({ cellValue }) => {
        return dictStore.formatter(String(cellValue), 'CREDIT_TYPE') || cellValue;
      },
    },
    { field: 'expiryDate', title: '额度到期日' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getCompanyListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <TypographyLink @click="openDetailPopup(true, row)"> 详情 </TypographyLink>
        </a-space>
      </template>
    </Grid>
    <Detail @register="registerDetail" />
  </Page>
</template>

<style></style>
