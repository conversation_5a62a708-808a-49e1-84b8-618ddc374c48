<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { GridApi, VxeTableGridOptions } from '#/adapter/vxe-table';

import { computed, reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import { Button, Col, DatePicker, Form, FormItem, Input, message, Row, Select, Textarea, Progress, Switch } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
// import DetailModal from "./detailModal.vue";
// import BusinessModal from "./businessModal.vue"
import {evaluateAddApi, type EvaluateBaseInfo} from "#/api";
// import { type AddInbound, type InboundReceiptItemBOs, type QueryGoodsRequest, addInboundApi, editInboundApi, inboundDetailApi, getPurchaseGoodsApi } from '#/api';

const emit = defineEmits(['register', 'ok']);

// 字典选项
const businessStructureOptions = ref([
  { label: '先采后销', value: '1' },
  { label: '先销后采', value: '2' },
]);

const projectModelOptions = ref([
  { label: '产业1', value: '1' },
  { label: '产业2', value: '2' },
  { label: '产业3', value: '3' },
  { label: '产业4', value: '4' },
  { label: '产业5', value: '5' },
]);

const executorCompanyOpations = ref([
  { label: 'aa', value: '1' },
  { label: 'bb', value: '2' },
]);

const executorCompanyOptions = ref([
  { label: '上海负责人', value: '1' },
  { label: '北京负责人', value: '2' },
]);

const { getDictList } = useDictStore();

// 默认数据
const defaultForm: Partial<AddInbound> = {
  id: undefined,
  createBy: 0,
  createTime: '',
  updateBy: 0,
  updateTime: '',
  version: 0,
  inboundReceiptCode: '',
  receiptDate:"",
  projectId: 0,
  projectName:  undefined,
  projectCode: '',
  warehouseId: 0,
  warehouseCode: '',
  warehouseName: '',
  customerCompanyCode: '',
  customerCompanyName: '',
  sourceDocumentType: '',
  deliveryReceiptId: 0,
  deliveryReceiptDisplay: '',
  amountWithTax: 0,
  invoicedAmountWithTax: 0,
  status: '',
  approvalStatus: '',
  invoiceStatus: '',
  remarks: '',
  isSnManaged: 0,
  inboundReceiptSourceRelBOS: [{
    sourceDocumentId: 123,
    sourceDocumentDisplay: '345',
    sourceDocumentType: '555'
  },
  ],
  inboundReceiptItemBOs: [],
};

let detailForm = reactive<Partial<AddInbound>>(defaultsDeep(defaultForm));

const colSpan = { md: 12, sm: 24 };

const rules: Record<string, Rule[]> = {
  // inboundReceiptCode: [{ required: true, message: '入库单编号', trigger: 'change' }],
  // projectName: [{ required: true, message: '所属项目名称', trigger: 'change' }],
  projectCode: [{ required: true, message: '所属项目编号', trigger: 'change' }],
  // sourceDocumentType: [{ required: true, message: '关联单据类型', trigger: 'change' }],
  // deliveryReceiptId: [{ required: true, message: '关联单据', trigger: 'change' }],
  // receiptDate: [{ required: true, message: '入库日期', trigger: 'change' }],
  // customerCompanyName: [{ required: true, message: '上/下游企业', trigger: 'change' }],
  // warehouseName: [{ required: true, message: '仓库名称', trigger: 'change' }],
};

const title = computed(() => {
  return detailForm.id ? '编辑项目' : '新增项目';
});

const init = async (data: any) => {
  if (data.id) {
    const res = await inboundDetailApi(data.id); // 调用真实 API

    // 深度复制确保响应性
    Object.keys(res).forEach(key => {
      detailForm[key] = res[key];
    });

    // 强制校验并转换inboundReceiptItemBOs字段
    if (!Array.isArray(res.inboundReceiptItemBOs) || res.inboundReceiptItemBOs === null) {
      detailForm.inboundReceiptItemBOs = [];
    } else {
      // 创建全新数组实例确保响应性
      detailForm.inboundReceiptItemBOs = [...res.inboundReceiptItemBOs];
    }

    // 强制刷新表格
    if (gridApi?.grid) {
      gridApi.grid.reloadData(detailForm.inboundReceiptItemBOs);
    }

    // 强制刷新表格
    if (gridApiLocation?.grid) {
      gridApiLocation.grid.reloadData(detailForm.inboundReceiptItemBOs);
    }
  } else {
    Object.assign(detailForm, defaultsDeep(defaultForm));
    detailForm.inboundReceiptItemBOs = defaultForm.inboundReceiptItemBOs ? [...defaultForm.inboundReceiptItemBOs] : [];
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);

const formRef = ref();
const save = async () => {
  try {
    await formRef.value.validate();

    changeOkLoading(true);
    // 显式类型断言确保类型正确
    const submitData = detailForm as Required<AddInbound>;

    // 根据是否存在id判断是新增还是编辑
    const res = detailForm.id
      ? await editInboundApi(submitData)
      : await addInboundApi(submitData);

    // 使用国际化提示保存成功
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } catch (error) {
    message.error('保存失败，请检查网络或输入内容');
    console.error('新增仓库失败:', error);
    changeOkLoading(false); // 防止 loading 卡住
  } finally {
    changeOkLoading(false);
  }
};

const labelCol = { style: { width: '150px' } };

// 在表格配置中添加key属性确保强制刷新
const grid: VxeTableGridOptions = {
  data: detailForm.inboundReceiptItemBOs,
  props: {
    key: computed(() => detailForm.id || 'new') // 添加key属性
  },
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'quantity',
      title: '收付记录编号',
      editRender: {},
      slots: { edit: 'edit_quantity' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentItemNumber',
      title: '收付单据类型',
      editRender: {},
      slots: { edit: 'edit_source_document_item_number' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentDisplay',
      title: '收付方向',
      editRender: {},
      slots: { edit: 'edit_source_document_display' },
      minWidth: '150px',
    },
    { field: 'serialNumbers', title: '付款方银行账号', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers', title: '收款方银行账号', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers', title: '实际收付日期', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers', title: '实际收付金额', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers', title: '已核销金额', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers', title: '未核销金额', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers', title: '已核销金额', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers', title: '关联单据类型', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers', title: '关联单据编号', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'remarks', title: '备注', editRender: {}, slots: { edit: 'edit_remarks' }, minWidth: '200px' },
  ],
  editConfig: {
    trigger: 'click',
    mode: 'row',
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar_location_tools',
    },
  },
};

const gridLocation: VxeTableGridOptions = {
  data: detailForm.inboundReceiptItemBOs,
  props: {
    key: computed(() => detailForm.id || 'new') // 添加key属性
  },
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'quantity',
      title: '源单单据编号',
      slots: { edit: 'edit_quantity' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentItemNumber',
      title: '源单单据类型',
      slots: { edit: 'edit_source_document_item_number' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentDisplay',
      title: '源单应付总金额',
      slots: { edit: 'edit_source_document_display' },
      minWidth: '150px',
    },
    { field: 'serialNumbers', title: '已核销金额', slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers', title: '未核销金额', slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers', title: '本次核销金额', slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'remarks', title: '备注', slots: { edit: 'edit_remarks' }, minWidth: '200px' },
  ],
  editConfig: {
    trigger: 'click',
    mode: 'row',
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar_location_tools',
    },
  },
};


// 选择收付数据
const selectCollectionPayData = () => {
  console.log(11111)
};

// 手动分页
const clickManualAllocation = () => {
  console.log(22222)
};

// 数据明细
const clickDataDetails = () => {
  console.log(33333)
};

// 选择业务数据
const selectBusinessData = () => {
  console.log(4444)
};

// 删除行
const removeLocationRow = async (gridApi: GridApi) => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (!Array.isArray(selectRecords)) {
      message.warning('请选择要删除的数据');
      return;
    }

    if (selectRecords.length > 0) {
      const { deleteInboundReceiptItemBOs } = detailForm;

      // 确保 deleteInboundReceiptItemBOs 是数组
      if (!Array.isArray(deleteInboundReceiptItemBOs)) {
        detailForm.deleteInboundReceiptItemBOs = [...selectRecords];
      } else {
        detailForm.deleteInboundReceiptItemBOs = [...deleteInboundReceiptItemBOs, ...selectRecords];
      }

      $grid.remove(selectRecords);
    } else {
      message.warning('请选择要删除的数据');
    }
  }
};

// 删除行
const removeLocationRow2 = async (gridApiLocation: GridLocation) => {
  const $grid = gridApiLocation.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (!Array.isArray(selectRecords)) {
      message.warning('请选择要删除的数据');
      return;
    }

    if (selectRecords.length > 0) {
      const { deleteInboundReceiptItemBOs } = detailForm;

      // 确保 deleteInboundReceiptItemBOs 是数组
      if (!Array.isArray(deleteInboundReceiptItemBOs)) {
        detailForm.deleteInboundReceiptItemBOs = [...selectRecords];
      } else {
        detailForm.deleteInboundReceiptItemBOs = [...deleteInboundReceiptItemBOs, ...selectRecords];
      }

      $grid.remove(selectRecords);
    } else {
      message.warning('请选择要删除的数据');
    }
  }
};

const projectCodeOptions = [
  {
    value: '26',
    label: '项目1'
  },
  {
    value: '2',
    label: '项目2'
  },
  {
    value: '3',
    label: '项目3'
  },
  {
    value: '4',
    label: '项目4'
  }
]

// 创建响应式的查询参数
const purchaseGoodsParams = reactive<QueryGoodsRequest>({
  projectId: undefined,
  ids: []
});

const getPurchaseGoodsFn = async (value: QueryGoodsRequest) => {
  if (value.projectId) {
    try {
      const res = await getPurchaseGoodsApi(purchaseGoodsParams);
      if (res) {
        detailForm.inboundReceiptItemBOs = [...res];

        // 强制刷新表格
        if (gridApi?.grid) {
          await gridApi.grid.reloadData(detailForm.inboundReceiptItemBOs);
        }

        // 强制刷新表格
        if (gridApiLocation?.grid) {
          await gridApiLocation.grid.reloadData(detailForm.inboundReceiptItemBOs);
        }
      }
    } catch (error) {
      console.error('获取商品信息失败:', error);
      message.error('获取商品信息失败');
    }
  } else {
    // 如果没有选择任何项目，清空商品列表
    detailForm.inboundReceiptItemBOs = [];
    if (gridApi?.grid) {
      await gridApi.grid.reloadData([]);
    }
    if (gridApiLocation?.grid) {
      await gridApiLocation.grid.reloadData([]);
    }
  }
}

const handleChangeProjectName = (selectedValues: string[]) => {
  // 清空之前的选择
  purchaseGoodsParams.ids = selectedValues;
  getPurchaseGoodsFn(purchaseGoodsParams)
};

const handleChangeProjectCode = (projectId: number) => {
  // 更新查询参数
  purchaseGoodsParams.projectId = projectId;
  getPurchaseGoodsFn(purchaseGoodsParams)
};

// 注册表格
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: grid,
  watch: {
    data: {
      handler: (newVal) => {
        if (gridApi?.grid && newVal) {
          setTimeout(() => {
            gridApi.grid.reloadData(newVal);
          }, 0);
        }
      },
      deep: true,
      immediate: true
    }
  }
});

// 注册表格
const [GridLocation, gridApiLocation] = useVbenVxeGrid({
  gridOptions: gridLocation,
  watch: {
    data: {
      handler: (newVal) => {
        if (gridApiLocation?.grid && newVal) {
          setTimeout(() => {
            gridApiLocation.grid.reloadData(newVal);
          }, 0);
        }
      },
      deep: true,
      immediate: true
    }
  }
});
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="detailForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基本信息 -->
      <BasicCaption content="基本信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="应付核销单编号" name="inboundReceiptCode">
            <Select v-model:value="detailForm.inboundReceiptCode" :options="projectModelOptions"/>
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="所属项目名称" name="projectName">
            <Select
              v-model:value="detailForm.projectName"
              mode="multiple"
              :options="projectModelOptions"
              @change="handleChangeProjectName"
              placeholder="请选择所属项目名称"
            />
          </FormItem>
        </Col>

        <!-- 所属项目编号 -->
        <Col v-bind="colSpan">
          <FormItem label="所属项目编号" name="projectCode">
            <Select
              v-model:value="detailForm.projectCode"
              :options="projectCodeOptions"
              @change="handleChangeProjectCode"
              placeholder="请选择所属项目编号"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="应付方企业" name="deliveryReceiptDisplay">
            <Select v-model:value="detailForm.deliveryReceiptDisplay" :options="businessStructureOptions" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="应收方企业" name="deliveryReceiptDisplay">
            <Select v-model:value="detailForm.deliveryReceiptDisplay" :options="businessStructureOptions" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="社会统一信用代码" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="社会统一信用代码" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="备注" name="remarks">
            <Textarea v-model:value="detailForm.remarks" :rows="3" />
          </FormItem>
        </Col>
      </Row>

      <!-- 核销信息 -->
      <BasicCaption content="核销信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="应付核销类型" name="deliveryReceiptDisplay">
            <Select v-model:value="detailForm.deliveryReceiptDisplay" :options="businessStructureOptions" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="应付核销日期" name="inboundReceiptCode">
            <DatePicker
              v-model:value="detailForm.receiptDate"
              value-format="YYYY-MM-DD"
            />
          </FormItem>
        </Col>
      </Row>

      <!-- 收付数据 -->
      <BasicCaption content="收付数据" />
      <div>
        <Grid>
          <template #toolbar_location_tools>
            <Button class="mr-2" type="primary" @click="selectCollectionPayData">选择收付数据</Button>
            <Button class="mr-2" danger @click="() => removeLocationRow(gridApiLocation)">删行</Button>
          </template>

          <template #edit_quantity="{ row }">
            {{ row.abc }}
          </template>

          <template #edit_source_document_item_number="{ row }">
            {{ row.abc }}
          </template>

          <template #edit_source_document_display="{ row }">
            {{ row.abc }}
          </template>

          <template #edit_serial_numbers="{ row }">
            {{ row.abc }}
          </template>

          <template #edit_serial_numbers1="{ row }">
            {{ row.abc }}
          </template>

          <template #edit_serial_numbers2="{ row }">
            {{ row.abc }}
          </template>

          <template #edit_serial_numbers3="{ row }">
            {{ row.abc }}
          </template>

          <template #edit_serial_numbers4="{ row }">
            {{ row.abc }}
          </template>

          <template #edit_serial_numbers9="{ row }">
            <Input v-model:value="row.remarks" placeholder="本次核销金额" />
          </template>

          <template #edit_serial_numbers10="{ row }">
            {{ row.abc }}
          </template>

          <template #edit_serial_numbers11="{ row }">
            {{ row.abc }}
          </template>

          <template #edit_remarks="{ row }">
            <Input v-model:value="row.remarks" placeholder="请输入备注" />
          </template>
        </Grid>
      </div>

      <!-- 业务数据 -->
      <BasicCaption content="业务数据" />
      <div>
        <GridLocation>
          <template #toolbar_location_tools>
            <Button class="mr-2" type="primary" @click="clickManualAllocation">手动分摊</Button>
            <Button class="mr-2" type="primary" @click="clickDataDetails">数据明细</Button>
            <Button class="mr-2" type="primary" @click="selectBusinessData">选择业务数据</Button>
            <Button class="mr-2" danger @click="() => removeLocationRow2(gridApiLocation)">删行</Button>
          </template>
          <template #edit_quantity1="{ row }">
            {{ row.serialNumbers }}
          </template>

          <template #edit_quantity2="{ row }">
            {{ row.serialNumbers }}
          </template>

          <template #edit_quantity3="{ row }">
            {{ row.serialNumbers }}
          </template>

          <template #edit_source_document_item_number="{ row }">
            {{ row.serialNumbers }}
          </template>

          <template #edit_serial_numbers3="{ row }">
            {{ row.serialNumbers }}
          </template>

          <template #edit_serial_numbers4="{ row }">
            <Input v-model:value="row.sourceDocumentItemNumber" placeholder="本次核销金额"/>
          </template>

          <template>
            刷新
          </template>

          <template #edit_remarks="{ row }">
            <Input v-model:value="row.remarks" placeholder="备注"/>
          </template>
        </GridLocation>
      </div>
    </Form>
  </BasicPopup>
</template>

<style scoped>
:where(.css-dev-only-do-not-override-1gaak89).ant-picker {
  width: 100%;
}
</style>
