import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '物流管理',
      order: 4,
    },
    name: 'LogisticsManage',
    path: '/logistics-manage',
    children: [
      {
        name: 'ShippingDocument',
        path: 'shipping-document',
        component: () => import('#/views/logistics-manage/shipping-document/index.vue'),
        meta: {
          // title: $t('page.projectManagement.initiation'),
          title: '发货单管理',
        },
      },
      {
        name: 'TransOrder',
        path: 'trans-order',
        component: () => import('#/views/logistics-manage/trans-order/index.vue'),
        meta: {
          // title: $t('page.projectManagement.initiation'),
          title: '运输指令单',
        },
      },
      {
        name: 'EntrustPickup',
        path: 'entrust-pickup',
        component: () => import('#/views/logistics-manage/entrust-pickup/index.vue'),
        meta: {
          // title: $t('page.projectManagement.initiation'),
          title: '委托提货函',
        },
      },
      {
        name: 'SealApplication',
        path: 'seal-application',
        component: () => import('#/views/logistics-manage/seal-application/index.vue'),
        meta: {
          // title: $t('page.projectManagement.initiation'),
          title: '用印申请',
        },
      },
    ],
  },
];

export default routes;
