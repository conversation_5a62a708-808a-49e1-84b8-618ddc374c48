<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { getStatisticsRiskApi } from '#/api';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const getStatistics = async () => {
  const res = await getStatisticsRiskApi();
  return res;
};

onMounted(async () => {
  await getStatistics();
  renderEcharts({
    grid: {
      top: '100',
      bottom: '3%',
      containLabel: true,
    },
    legend: {
      top: '10',
      data: ['本月新增项目数量', '本月新增项目投放金额', '本月结清项目数量', '结清项目累计收回金额'],
    },
    xAxis: [
      {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        axisPointer: {
          type: 'shadow',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量',
      },
      {
        type: 'value',
        name: '金额',
      },
    ],
    series: [
      {
        name: '本月新增项目数量',
        type: 'bar',
        itemStyle: { color: '#66B3FF' }, // 浅蓝色（匹配需求色）
        tooltip: {
          formatter: '{seriesName}：{value} 个',
        },
        data: [2, 4.9, 7, 23.2, 25.6, 76.7, 135.6],
      },
      {
        name: '本月新增项目投放金额',
        type: 'line',
        yAxisIndex: 1,
        itemStyle: { color: '#FF4D4F' }, // 红色（匹配需求色）
        tooltip: {
          formatter: '{seriesName}：{value} 万元',
        },
        data: [2.6, 5.9, 9, 26.4, 28.7, 70.7, 175.6],
      },
      {
        name: '本月结清项目数量',
        type: 'bar',
        itemStyle: { color: '#36CFC9' },
        tooltip: {
          formatter: '{seriesName}：{value} 个',
        },
        data: [2, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3],
      },
      {
        name: '结清项目累计收回金额',
        type: 'line',
        yAxisIndex: 1,
        itemStyle: { color: '#FAAD14' },
        tooltip: {
          formatter: '{seriesName}：{value} 万元',
        },
        data: [2, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3],
      },
    ],
  });
});
</script>

<template>
  <EchartsUI ref="chartRef" />
</template>
