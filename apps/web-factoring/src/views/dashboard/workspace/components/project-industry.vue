<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { isEmpty } from 'lodash-es';

import { getStatisticsProjectIndustryApi } from '#/api';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const colors = [
  '#f7ba1e',
  '#f56a6a',
  '#722ed1',
  '#13c2c2',
  '#fa8c16',
  '#52c41a',
  '#4895ef',
  '#86909c',
  '#ff4d4f',
  '#bf73f6',
  '#1890ff',
  '#faad14',
  '#73d13d',
  '#ff7a45',
  '#2f54eb',
  '#e53935',
  '#6f42c1',
  '#00b42a',
  '#34c759',
  '#6495ed',
];
const getStatistics = async () => {
  const res = await getStatisticsProjectIndustryApi();
  const data = {
    name: [],
    value: [],
  };
  if (!isEmpty(res)) {
    res.forEach((item, index) => {
      data.name.push(item.industry);
      item.value = Number(item.ratio);
      item.name = item.industry;
      item.itemStyle = {
        color: colors[index],
      };
      data.value.push(item);
    });
  }
  return data;
};

onMounted(async () => {
  const infoDetail = await getStatistics();
  renderEcharts({
    angleAxis: {
      type: 'category',
      data: infoDetail.name,
      axisLabel: {
        interval: 1,
        rich: {
          singleLine: {
            width: 80, // 单行最大宽度（与maxWidth一致）
          },
        },
        formatter(text) {
          const maxWidth = 80; // 单行最大宽度（像素）
          const ctx = document.createElement('canvas').getContext('2d');
          ctx.font = '12px sans-serif'; // 匹配字体样式

          let currentWidth = 0;
          let result = '';
          const ellipsis = '...';
          const ellipsisWidth = ctx.measureText(ellipsis).width;

          for (let i = 0; i < text.length; i++) {
            const char = text[i];
            const charWidth = ctx.measureText(char).width;

            // 累加宽度未超限，继续拼接
            if (currentWidth + charWidth <= maxWidth - ellipsisWidth) {
              result += char;
              currentWidth += charWidth;
            } else {
              // 超限后添加省略号并终止
              result += ellipsis;
              break;
            }

            // 若文本未超限，直接返回完整内容
            if (i === text.length - 1) {
              result = text;
            }
          }

          return `{singleLine|${result}}`; // 应用左对齐样式
        },
      },
    },
    radiusAxis: {
      min: 0,
      max: 100,
      interval: 20,
    },
    polar: {
      center: ['50%', '30%'],
      radius: '50%', // 缩小极坐标整体范围
    },
    tooltip: {
      confine: true,
      trigger: 'item',
      formatter: '{b} : {c} ({d}%)',
    },
    legend: {
      show: true,
      top: '60%',
      data: infoDetail.name,
    },
    calculable: true,
    series: [
      {
        stack: 'a',
        type: 'pie',
        radius: '55%',
        center: ['50%', '35%'],
        roseType: 'area',
        label: {
          normal: {
            show: false,
          },
          emphasis: {
            show: false,
          },
        },
        data: infoDetail.value,
      },
    ],
  });
});
</script>

<template>
  <EchartsUI ref="chartRef" />
</template>
