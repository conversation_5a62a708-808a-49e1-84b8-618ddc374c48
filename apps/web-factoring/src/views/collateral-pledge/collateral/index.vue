<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { CollateralPageVO, CollateralVO } from '#/api';

import { ref } from 'vue';

import { useModalUrl } from '@vben/base-ui';
import { Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep, defineFormOptions } from '@vben/utils';

import { Button, DatePicker, Form, FormItem, Input, message, Space, TypographyLink } from 'ant-design-vue';
import dayjs from 'dayjs';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addCollateral<PERSON><PERSON>,
  delCollateralApi,
  editCollateralApi,
  getCollateralPageListApi,
  infoCollateralApi,
  uploadCollateralApi,
} from '#/api';

import Detail from './detail.vue';
import Edit from './edit.vue';

const dictStore = useDictStore();
const labelCol = { style: { width: '150px' } };
// 修复表单字段与后端接口匹配的bug
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'mortgageName',
      label: '抵押物名称',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '关联项目',
    },
    {
      component: 'Input',
      fieldName: 'mortgageeCompanyName',
      label: '抵押权人',
    },
    {
      component: 'Input',
      fieldName: 'mortgageCompanyName',
      label: '抵押人',
    },
    {
      component: 'Select',
      fieldName: 'statusList',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList('FCT_NOT_REVIEW_STATUS'),
      },
    },
  ],
  showCollapseButton: false,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'mortgageName', title: '抵押物名称', width: 280 },
    { field: 'projectName', title: '关联项目', width: 280 },
    { field: 'mortgageeCompanyName', title: '抵押权人' },
    { field: 'mortgageCompanyName', title: '抵押人' },
    { field: 'rightsCertNo', title: '他项权证编号' },
    { field: 'assessedAmount', title: '抵押物评估价值（元）', formatter: 'formatMoney' },
    {
      field: 'mortgageType',
      title: '抵押方式',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_MORTGAGE_TYPE',
        },
      },
    },
    { field: 'mortgageAmount', title: '抵押价值/最高债权数额（元）', formatter: 'formatMoney' },
    { field: 'mortgageTerm', title: '抵押期限' },
    { field: 'propertyLocation', title: '不动产坐落' },
    { field: 'issuingDate', title: '出证日期', formatter: 'formatDate' },
    {
      field: 'status',
      title: '操作状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_NOT_REVIEW_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 200,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getCollateralPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [detailForm, { openPopup: openDetailFormPopup }] = usePopup();
const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

// 打开新增页面
const add = () => {
  openFormPopup(true, {});
};

const edit = async (rowData: CollateralPageVO) => {
  try {
    const res = await infoCollateralApi(rowData.id);
    const data = cloneDeep(res);
    openFormPopup(true, data);
  } catch {}
};

const editSuccess = async (data: CollateralVO) => {
  try {
    data.id ? await editCollateralApi(data) : await addCollateralApi(data);
    message.success($t('base.resSuccess'));
    openFormPopup(false, {});
    await gridApi.formApi.submitForm(); // 刷新列表数据
  } catch {}
};
const detail = async (_row: CollateralPageVO) => {
  try {
    const res = await infoCollateralApi(_row.id);
    const data = cloneDeep(res);
    openDetailFormPopup(true, data);
  } catch {
    // message.error('删除失败: ' + error.message);
  }
};

const del = async (rowData: CollateralPageVO) => {
  try {
    Modal.confirm({
      title: $t('base.del'),
      content: $t('base.confirmDelContent'),
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await delCollateralApi(rowData.id);
          message.success($t('base.resSuccess'));
          await gridApi.formApi.submitForm(); // 刷新列表数据
        } catch {
          message.error('删除失败');
        }
      },
    });
  } catch {
    message.error('删除失败');
  }
};

const fileFormRef = ref();
const fileForm = ref({
  issuingDate: '',
  id: '',
  rightsCertNo: '',
  attachmentList: [],
});

const upLoadReport = (_row: CollateralPageVO) => {
  fileForm.value.id = _row.id as unknown as string;
  fileForm.value.issuingDate = dayjs(_row.issuingDate).valueOf().toString();
  fileForm.value.rightsCertNo = _row.rightsCertNo;
  modalApi.open();
};
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    try {
      // await fileFormRef.value.validate();
      if (fileForm.value.attachmentList.length === 0) {
        message.error('请上传抵押物证件');
        return;
      }
      await uploadCollateralApi(fileForm.value);
      message.success($t('base.resSuccess'));
      await modalApi.close();
      await gridApi.reload();
    } catch {
      // message.error('操作失败');
    }
  },
  onClosed: () => {
    fileForm.value = {
      issuingDate: '',
      id: '',
      rightsCertNo: '',
      attachmentList: [],
    };
  },
});

// const rules: Record<string, Rule[]> = {
//   attachmentList: [{ required: true, message: '请上传抵押物证件', trigger: 'change' }],
// };

// 使用组合式函数处理URL参数自动打开弹层
useModalUrl({
  handlers: {
    // 编辑弹层
    edit: (params) => {
      const data: CollateralPageVO = {
        id: params.id,
      };
      edit(data);
    },

    // 详情弹层
    detail: (params) => {
      const data: CollateralPageVO = {
        id: params.id,
      };
      detail(data);
    },
  },
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink v-if="row.status === 'SUBMIT'" @click="edit(row)"> {{ $t('base.edit') }} </TypographyLink>
          <TypographyLink @click="detail(row)"> {{ $t('base.detail') }} </TypographyLink>
          <TypographyLink v-if="row.status === 'SUBMIT'" type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <TypographyLink v-if="row.status === 'EFFECTIVE' && !row.uploadFileId" @click="upLoadReport(row)">
            上传抵押物证件
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Edit @register="registerForm" @ok="editSuccess" />
    <Detail @register="detailForm" />
    <Modal title="上传抵押物证件" class="w-[1000px]">
      <Form ref="fileFormRef" :model="fileForm" :label-col="labelCol" :rules="rules" :wrapper-col="{ span: 20 }">
        <FormItem label="他项权证编号" name="rightsCertNo">
          <Input v-model:value="fileForm.rightsCertNo" />
        </FormItem>
        <FormItem label="出证日期" name="issuingDate">
          <DatePicker style="width: 100%" v-model:value="fileForm.issuingDate" value-format="x" />
        </FormItem>
        <FormItem label="抵押物证件" required>
          <BaseAttachmentList
            v-model="fileForm.attachmentList"
            :business-id="fileForm.id"
            business-type="FCT_PROJECT_MORTGAGE_UPLOAD"
            edit-mode
          >
            <template #header>
              <span></span>
            </template>
          </BaseAttachmentList>
        </FormItem>
      </Form>
    </Modal>
  </Page>
</template>

<style></style>
