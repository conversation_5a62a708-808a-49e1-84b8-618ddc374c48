<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { poolSupervisionDisposalVo, poolWarningPageVO } from '#/api';

import { reactive, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { Form, FormItem, Select, Space, Textarea, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { editPoolSupervisionDisposalApi, getPoolWarningPageListApi } from '#/api';

const dictStore = useDictStore();
const rulesForm = reactive<poolSupervisionDisposalVo>({
  id: undefined,
  monitoringMeasures: [],
  remarks: '',
});
// 修复表单字段与后端接口匹配的bug
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Input',
      fieldName: 'receivablePoolName',
      label: '应收账款池名称',
    },
    {
      component: 'Input',
      fieldName: 'poolCompanyName',
      label: '池保理融资企业',
    },
  ],
  commonConfig: {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  },
  showCollapseButton: false,
  submitOnEnter: true,
});
const labelCol = { style: { width: '150px' } };
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'projectName', title: '项目名称', width: 280 },
    { field: 'receivablePoolName', title: '应收账款池名称', width: 280 },
    { field: 'poolCompanyName', title: '池保理融资企业' },
    { field: 'poolTotalAmount', title: '池内资产总额（元）', formatter: 'formatMoney' },
    // { field: 'poolFinanceRate', title: '池融资比例' },
    { field: 'expiredReceivableCodes', title: '已到期应收账款' },
    { field: 'expiredNumber', title: '已到期笔数' },
    { field: 'expiredDays', title: '已到期天数' },
    {
      field: 'status',
      title: '处置状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_WARNING_LOG_WARNING_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getPoolWarningPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const rulesFormRef = ref();
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    try {
      await rulesFormRef.value.validate();
      await editPoolSupervisionDisposalApi(rulesForm);
      await modalApi.close();
      await gridApi.formApi.submitForm();
    } catch {}
  },
});
const isDisposed = ref(false);
const edit = (row: poolWarningPageVO) => {
  rulesForm.id = row.id;
  isDisposed.value = row.status === 'disposed';
  modalApi.open();
};
const rules: Record<string, Rule[]> = {
  monitoringMeasures: [{ required: true, message: '请输入监测处置措施' }],
};
const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ row.status === 'un_disposed' ? '监管处置' : '查看监管处置' }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Modal title="监管处置" class="w-[800px]">
      <Form ref="rulesFormRef" :model="rulesForm" :label-col="labelCol" :rules="rules" :wrapper-col="{ span: 20 }">
        <FormItem label="监测处置措施" name="monitoringMeasures">
          <Select
            :disabled="isDisposed"
            v-model:value="rulesForm.monitoringMeasures"
            :options="dictStore.getDictList('FCT_WARNING_RECEIVABLE_POOL_MEASURES')"
          />
        </FormItem>
        <FormItem label="备注">
          <Textarea :disabled="isDisposed" v-model:value="rulesForm.remarks" :rows="4" placeholder="请输入备注" />
        </FormItem>
      </Form>
    </Modal>
  </Page>
</template>

<style></style>
