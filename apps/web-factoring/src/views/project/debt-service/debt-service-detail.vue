<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { DebtServiceInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP, DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';

import { getDebtServiceInfoApi } from '#/api';

defineEmits(['register']); // 保持注册事件，用于弹窗注册
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const dictStore = useDictStore();
const init = async (data: DebtServiceInfo) => {
  // 仅保留数据获取逻辑，去掉编辑相关的初始化（如表格编辑初始化）
  debtServiceForm.value = data.id ? await getDebtServiceInfoApi(data.id as number) : { ...data };

  // 表格数据加载（仅展示，去掉编辑相关逻辑）
  debtServiceForm.value.detailList = debtServiceForm.value.detailList ?? [];
  await CalculationGridApi.grid.reloadData(debtServiceForm.value.detailList ?? []);

  if (debtServiceForm.value.penaltyType === 'ladder') {
    debtServiceForm.value.penaltySteps = debtServiceForm.value.penaltySteps ?? [];
    await LadderGridApi.grid.reloadData(debtServiceForm.value.penaltySteps ?? []);
  }
};
const [registerPopup] = usePopupInner(init); // 去掉 ok 相关逻辑，仅保留初始化

const debtServiceForm = ref<DebtServiceInfo>({});

// 阶梯罚息表格（仅展示）
const ladderGridOptions = {
  columns: [
    {
      field: 'daysInfo',
      title: '逾期天数（天）',
      slots: { default: 'days_info' },
    },
    {
      field: 'rate',
      title: '年化罚息利率（%）',
    },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;
const [LadderGrid, LadderGridApi] = useVbenVxeGrid({
  gridOptions: ladderGridOptions,
});
const calculationGridOptions = {
  columns: [
    {
      field: 'id',
      title: '还款期数ID',
      minWidth: '100px',
    },
    {
      field: 'repayPeriods',
      title: '还款期数',
      minWidth: '100px',
    },
    {
      field: 'businessType',
      title: '业务类型',
      minWidth: '120px',
    },
    {
      field: 'currentDate',
      title: '当期还本/付息日',
      minWidth: '180px',
    },
    {
      field: 'totalAmount',
      title: '当期净现金流(元)',
      minWidth: '180px',
      formatter: 'formatMoney',
    },
    {
      field: 'principalAmount',
      title: '应还本金(元)',
      minWidth: '180px',
      formatter: 'formatMoney',
    },
    {
      field: 'interestAmount',
      title: '应还利息(元)',
      minWidth: '180px',
      formatter: 'formatMoney',
    },
    {
      field: 'serviceAmount',
      title: '应收服务费(元)',
      minWidth: '180px',
      formatter: 'formatMoney',
    },
    {
      field: 'graceInterestAmount',
      title: '应还宽限期利息(元)',
      minWidth: '180px',
      formatter: 'formatMoney',
    },
    {
      field: 'overdueInterestAmount',
      title: '应还逾期罚息(元)',
      minWidth: '180px',
      formatter: 'formatMoney',
    },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;
const [CalculationGrid, CalculationGridApi] = useVbenVxeGrid({
  gridOptions: calculationGridOptions,
});
</script>

<template>
  <BasicPopup title="还本付息计划详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <!-- 基础信息描述 -->
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="关联单一项目">
          {{ debtServiceForm.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="还本付息试算名称">
          {{ debtServiceForm.calculationName }}
        </a-descriptions-item>
        <a-descriptions-item label="应收账款金额（元）">
          {{ formatMoney(debtServiceForm.receivableAmount) }}
        </a-descriptions-item>
        <a-descriptions-item label="基准定价（%/年）">
          {{ debtServiceForm.pricingBasicRatio }}
        </a-descriptions-item>
        <a-descriptions-item label="浮动定价（%/年）">
          {{ debtServiceForm.pricingFloatingRatio }}
        </a-descriptions-item>
        <a-descriptions-item label="综合收益率（%）">
          {{ debtServiceForm.comprehensiveRate }}
        </a-descriptions-item>
        <a-descriptions-item label="备注">
          {{ debtServiceForm.remarks }}
        </a-descriptions-item>
      </a-descriptions>

      <BasicCaption content="还本付息方案" />
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="计划融资金额（元）">
          {{ formatMoney(debtServiceForm.financingAmount) }}
        </a-descriptions-item>
        <a-descriptions-item label="融资比例（%）">
          {{ debtServiceForm.financingRatio }}
        </a-descriptions-item>
        <a-descriptions-item label="宽限期天数（天）">
          {{ debtServiceForm.gracePeriodDays }}
        </a-descriptions-item>
        <a-descriptions-item label="宽限期费率（%/年）">
          {{ debtServiceForm.gracePeriodRate }}
        </a-descriptions-item>
        <a-descriptions-item label="服务费（元）">
          {{ formatMoney(debtServiceForm.serviceFeeAmount) }}
        </a-descriptions-item>
        <a-descriptions-item label="罚息类型">
          {{ dictStore.formatter(debtServiceForm.penaltyType, 'FCT_PENALTY_TYPE') }}
        </a-descriptions-item>
        <a-descriptions-item v-if="debtServiceForm.penaltyType === 'fixed'" label="固定罚息利率（%）">
          {{ debtServiceForm.penaltyInterestRate }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 阶梯罚息表格（仅展示） -->
      <LadderGrid v-if="debtServiceForm.penaltyType === 'ladder'">
        <template #days_info="{ row }">
          <a-space>
            <span>{{ row.startDays }}</span>
            <span>{{ row.endDays }}</span>
          </a-space>
        </template>
      </LadderGrid>

      <BasicCaption content="还本付息试算" />
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="还本付息计划规划方式">
          {{ dictStore.formatter(debtServiceForm.planningMethod, 'FCT_PLANNING_METHOD') }}
        </a-descriptions-item>
        <a-descriptions-item label="测算综合收益率（%/年）">
          {{ debtServiceForm.compositeYieldRate }}
        </a-descriptions-item>
        <a-descriptions-item label="预计业务投放日">
          {{ debtServiceForm.expectedLaunchDate }}
        </a-descriptions-item>
        <a-descriptions-item label="预估最后还款日">
          {{ debtServiceForm.expectedDueDate }}
        </a-descriptions-item>
        <a-descriptions-item label="还本方式">
          {{ dictStore.formatter(debtServiceForm.principalRepaymentMethod, 'FCT_REPAY_PRINCIPAL_METHOD') }}
        </a-descriptions-item>
        <a-descriptions-item label="还息方式">
          {{ dictStore.formatter(debtServiceForm.interestRepaymentMethod, 'FCT_REPAY_INTEREST_METHOD') }}
        </a-descriptions-item>
        <a-descriptions-item label="分期还本频次">
          {{ dictStore.formatter(debtServiceForm.principalPeriod, 'FCT_FREQUENCY') }}
        </a-descriptions-item>
        <a-descriptions-item label="分期还息频次">
          {{ dictStore.formatter(debtServiceForm.interestPeriod, 'FCT_FREQUENCY') }}
        </a-descriptions-item>
        <a-descriptions-item label="默认当期还本日">
          {{ dictStore.formatter(debtServiceForm.repayPrincipalDay, 'FCT_CURRENT_REPAY_DATE') }}
        </a-descriptions-item>
        <a-descriptions-item label="默认当期还息日">
          {{ dictStore.formatter(debtServiceForm.repayInterestDay, 'FCT_CURRENT_REPAY_DATE') }}
        </a-descriptions-item>
        <a-descriptions-item label="预估计息天数（天）">
          {{ debtServiceForm.expectedInterestDays }}
        </a-descriptions-item>
        <a-descriptions-item label="预估还款期数">
          {{ debtServiceForm.expectedRepayPeriods }}
        </a-descriptions-item>
      </a-descriptions>

      <CalculationGrid />
    </div>
  </BasicPopup>
</template>

<style></style>
