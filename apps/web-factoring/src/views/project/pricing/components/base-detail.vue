<script setup lang="ts">
import { useDictStore } from '@vben/stores';
import { formatMoney } from '@vben/utils';

defineProps({
  pricingForm: { type: Object, default: () => ({}) },
  descriptionsProp: { type: Object, default: () => ({}) },
});
const dictStore = useDictStore();
</script>

<template>
  <div>
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="关联项目">
        {{ pricingForm.projectName }}
      </a-descriptions-item>
      <a-descriptions-item label="项目类型">
        {{ dictStore.formatter(pricingForm.projectType, 'FCT_PROJECT_TYPE') }}
      </a-descriptions-item>
      <a-descriptions-item label="项目定价名称">
        {{ pricingForm.pricingName }}
      </a-descriptions-item>
      <a-descriptions-item label="授信客户" v-if="pricingForm.projectType === 'comprehensive'">
        {{ pricingForm.creditCompanyName }}
      </a-descriptions-item>
      <a-descriptions-item label="合作客户" v-if="pricingForm.projectType === 'single'">
        {{ pricingForm.targetCompanyName }}
      </a-descriptions-item>
      <a-descriptions-item label="客户类别">
        {{ dictStore.formatter(pricingForm.customerCategory, 'FCT_COMPANY_LIMIT_COMPANY_TYPE') }}
      </a-descriptions-item>
      <a-descriptions-item label="客户分级">
        {{ dictStore.formatter(pricingForm.customerLevel, 'COMPANY_RATING') }}
      </a-descriptions-item>
      <a-descriptions-item label="行业属性">
        {{ dictStore.formatter(pricingForm.industryAttributes, 'FCT_PRICING_INDUSTRY_ATTRIBUTES') }}
      </a-descriptions-item>
      <a-descriptions-item label="行业属性其他说明" v-if="pricingForm.industryAttributes === '10'">
        {{ pricingForm.industryAttributesDesc }}
      </a-descriptions-item>
      <a-descriptions-item label="授信额度测算结果">
        {{ pricingForm.creditCalculateAmount }}
      </a-descriptions-item>
      <a-descriptions-item label="区域特性">
        {{ dictStore.formatter(pricingForm.areaCharacteristics, 'FCT_PRICING_AREA_CHARACTERISTICS') }}
      </a-descriptions-item>
      <a-descriptions-item label="底层项目">
        {{ dictStore.formatter(pricingForm.bottomProject, 'FCT_PRICING_BOTTOM_PROJECT') }}
      </a-descriptions-item>
      <a-descriptions-item label="担保方式">
        {{ dictStore.formatter(pricingForm.guaranteeMethod, 'FCT_PRICING_GUARANTEE_METHOD') }}
      </a-descriptions-item>
      <a-descriptions-item label="担保方式说明" v-if="pricingForm.guaranteeMethod === '5'">
        {{ pricingForm.guaranteeMethodDesc }}
      </a-descriptions-item>
      <a-descriptions-item label="应收账款金额（元）" v-if="pricingForm.projectType === 'single'">
        {{ formatMoney(pricingForm.receivableAmount) }}
      </a-descriptions-item>
      <a-descriptions-item label="其他情况" :span="2">
        {{ pricingForm.pricingOtherDesc }}
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>

<style scoped></style>
