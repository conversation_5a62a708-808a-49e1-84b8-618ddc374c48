<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { PaymentRecordInfo } from '#/api';

import { ref } from 'vue';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getPaymentRecordPageListApi } from '#/api';
import PaymentRecordDetail from '#/views/fund/payment-record/payment-record-detail.vue';

const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'confirmCode', title: '付款记录编号', minWidth: 200 },
    { field: 'confirmInvestAmount', title: '确认投放金额（元）', formatter: 'formatMoney' },
    { field: 'confirmInvestDate', title: '确认投放日期', formatter: 'formatDate' },
    {
      field: 'confirmPaymentMethod',
      title: '付款方式',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_PAYMENT_METHOD',
        },
      },
    },
    { field: 'payeeCompanyName', title: '收款单位' },
    { field: 'payeeBankBranch', title: '收款单位开户行' },
    { field: 'payeeBankAccount', title: '收款单位银行账号' },
    { field: 'remarks', title: '备注' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 120,
      slots: { default: 'action' },
    },
  ],
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: async ({ page }, formValues) => {
        return await getPaymentRecordPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
          projectId: form.value.id,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
const form = ref({});
const init = (data: any) => {
  form.value = data;
  gridApi.reload();
};
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: PaymentRecordInfo) => {
  openDetailPopup(true, row);
};
defineExpose({ init });
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <PaymentRecordDetail @register="registerDetail" />
  </Page>
</template>

<style></style>
