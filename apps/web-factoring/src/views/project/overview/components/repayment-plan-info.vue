<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref } from 'vue';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getRepaymentPlanPageListApi } from '#/api';
import RepaymentPlanDetail from '#/views/fund/repayment-plan/repayment-plan-detail.vue';

const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'repaymentPlanCode', title: '还款计划编号', minWidth: 180 },
    {
      field: 'creditApplyName',
      title: '用信申请名称',
      minWidth: 200,
    },
    {
      field: 'paymentApplyCode',
      title: '关联付款申请',
      minWidth: 200,
    },
    {
      field: 'paymentConfirmCode',
      title: '关联付款记录',
      minWidth: 200,
    },
    {
      field: 'companyName',
      title: '融资企业',
      minWidth: 160,
    },
    {
      field: 'principalAmount',
      title: '应还本金金额（元）',
      minWidth: 160,
      formatter: 'formatMoney',
    },
    {
      field: 'actualPrincipalAmount',
      title: '已还本金金额（元）',
      minWidth: 160,
      formatter: 'formatMoney',
    },
    {
      field: 'actualTotalAmount',
      title: '实收净现金流（元）',
      minWidth: 160,
      formatter: 'formatMoney',
    },
    {
      field: 'launchDate',
      title: '起息日',
      minWidth: 120,
      formatter: 'formatDate',
    },
    {
      field: 'dueDate',
      title: '最后还款日',
      minWidth: 120,
      formatter: 'formatDate',
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 140,
      slots: { default: 'action' },
    },
  ],
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: async ({ page }, formValues) => {
        return await getRepaymentPlanPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
          projectId: form.value.id,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
const form = ref({});
const init = (data: any) => {
  form.value = data;
  gridApi.reload();
};
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: any) => {
  openDetailPopup(true, row);
};
defineExpose({ init });
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <RepaymentPlanDetail @register="registerDetail" />
  </Page>
</template>

<style></style>
