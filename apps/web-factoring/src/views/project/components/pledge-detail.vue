<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { InitiationInfo } from '#/api';

import { watch } from 'vue';

import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { formatDate } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

const props = defineProps({
  info: { type: Object, default: () => ({}) },
});
const GridOptions = {
  columns: [
    { field: 'pledgeName', title: '质押物名称', width: 140 },
    { field: 'pledgorCompanyName', title: '出质人' },
    { field: 'pledgeeCompanyName', title: '质权人' },
    { field: 'mainContractCode', title: '质押主合同号码' },
    { field: 'pledgeContractCode', title: '质押合同编号' },
    { field: 'mainContractAmount', title: '主合同金额（元）', formatter: 'formatMoney' },
    { field: 'mortgageAmount', title: '债务履行期限', slots: { default: 'mortgageAmount-span' } },
    { field: 'pledgeAmount', title: '质押财产价值 （元）', formatter: 'formatMoney' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 100,
      slots: { default: 'action' },
    },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: GridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: InitiationInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});

const viewDetail = (row: any) => {
  const params = new URLSearchParams();
  params.append('modal', 'detail');
  params.append('id', row.pledgeId);
  const url = `${import.meta.env.VITE_FCT_JUMP_URL}/collateralPledge/pledgeManage?${params.toString()}`;
  window.open(url);
};

watch(
  () => props.info,
  (val = {}) => {
    gridApi.grid.reloadData(val.pledgeList ?? []);
  },
  { deep: true },
);
</script>

<template>
  <div>
    <BasicCaption content="质押物" />
    <Grid>
      <template #mortgageAmount-span="{ row }">
        {{ formatDate(row.startDebtPeriodDate) }} - {{ formatDate(row.endDebtPeriodDate) }}
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
  </div>
</template>
