<script setup lang="ts">
import type { PaymentRecordInfo } from '#/api';

import { reactive, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { message, Select } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep, isEmpty, omit } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import {
  addRepaymentChangeApi,
  editRepaymentChangeApi,
  getRepaymentChangeInfoApi,
  getRepaymentPlanInfoApi,
  getRepaymentPlanListApi,
} from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';
import InterestFees from '#/views/fund/components/interest-fees.vue';
import RepaymentChange from '#/views/fund/components/repayment-change.vue';

const emit = defineEmits(['ok', 'register']);
const dictStore = useDictStore();
const { startWorkflow, initWorkflow, WorkflowPreviewModal } = useWorkflowBase();
const init = async (data: PaymentRecordInfo) => {
  await initWorkflow({ formKey: 'fct_repayment_plan_change', businessKey: data.id });
  changeForm.value = {};
  if (data.id) {
    const info = data.id ? await getRepaymentChangeInfoApi(data.id as number) : data;
    info.dueDate = info.dueDate ? dayjs(info.dueDate).valueOf().toString() : '';
    changeForm.value = { ...changeForm.value, ...info };
    RepaymentChangeRef.value.init(changeForm.value);
  }
  if (data.formType === 'repaymentPlan') {
    changeForm.value.repaymentPlanId = Number(data.repaymentPlanId);
    data.value = data.repaymentPlanId;
    await selectRepaymentPlan(data.repaymentPlanId, data);
  }
};

const selectRepaymentPlan = async (_value: number, data: PaymentRecordInfo) => {
  const info = await getRepaymentPlanInfoApi(data.value);
  const repaymentPlan = omit(info, 'id');
  repaymentPlan.dueDate = repaymentPlan.dueDate ? dayjs(repaymentPlan.dueDate).valueOf().toString() : '';
  changeForm.value = { ...changeForm.value, ...repaymentPlan };
  if (!isEmpty(changeForm.value.detailList)) {
    let targetIndex = -1;
    // 从后往前查找最后一个符合条件的行
    for (let i = changeForm.value.detailList.length - 1; i >= 0; i--) {
      if (
        changeForm.value.detailList[i].status === 'no' &&
        changeForm.value.detailList[i].settlementStatus === 'settled'
      ) {
        targetIndex = i;
        break;
      }
    }

    const newRow = {
      settlementStatus: 'settled',
      status: 'no',
      changeRecordFlag: 1,
      currentOperationDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    };

    if (targetIndex >= 0) {
      // 在最后一个符合条件的行后插入
      changeForm.value.detailList.splice(targetIndex + 1, 0, newRow);
    } else {
      // 在第一行下方插入
      changeForm.value.detailList.splice(1, 0, newRow);
    }
  }
  RepaymentChangeRef.value.init(changeForm.value);
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const FormRef = ref();
const changeForm = ref<PaymentRecordInfo>({});

const loading = reactive({
  submit: false,
});
const RepaymentChangeRef = ref();
const save = async (type: string) => {
  await FormRef.value.validate();
  const repaymentResult = await RepaymentChangeRef.value.save();
  if (!repaymentResult) {
    return;
  }
  let api = addRepaymentChangeApi;
  if (changeForm.value.id) {
    api = editRepaymentChangeApi;
  }
  const formData = cloneDeep(changeForm.value);
  formData.dueDate = Number(formData.dueDate);
  if (type === 'submit') {
    formData.isSubmit = true;
    const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
    formData.processDefinitionKey = processDefinitionKey;
    formData.startUserSelectAssignees = startUserSelectAssignees;
  }
  loading.submit = true;
  try {
    const res = await api(formData as PaymentRecordInfo);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};
const rules = {
  repaymentPlanId: [{ required: true, message: '请选择关联还款计划编号', trigger: 'change' }],
  dueDate: [{ required: true, message: '请选择最后还款日', trigger: 'change' }],
  gracePeriodDays: [{ required: true, message: '请输入宽限期天数' }],
  currentOperationDate: [{ required: true, content: '请选择当期还本/付息日', trigger: 'change' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 7 }, wrapperCol: { span: 17 } };
const colSpan = COL_SPAN_PROP;
</script>

<template>
  <BasicPopup v-bind="$attrs" title="还款计划变更" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save('save')">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="changeForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="还款计划变更编号">
              {{ changeForm.repaymentPlanChangeCode }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="关联还款计划编号" name="repaymentPlanId">
              <ApiComponent
                v-model="changeForm.repaymentPlanId as unknown as string"
                :component="Select"
                :api="getRepaymentPlanListApi"
                label-field="repaymentPlanCode"
                value-field="id"
                model-prop-name="value"
                @change="selectRepaymentPlan"
                show-search
                :filter-option="(input: string, option: any) => option.label.includes(input)"
                placeholder="请选择关联还款计划编号"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目名称">
              {{ changeForm.projectName }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目类型">
              {{ dictStore.formatter(changeForm.projectType, 'FCT_PROJECT_TYPE') }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="关联用信申请">
              {{ changeForm.creditApplyName }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="关联付款申请编号">
              {{ changeForm.paymentApplyCode }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="关联付款记录">
              {{ changeForm.paymentConfirmCode }}
            </a-form-item>
          </a-col>
        </a-row>
        <InterestFees :interest-form="changeForm" />
        <RepaymentChange ref="RepaymentChangeRef" v-model="changeForm" calculation-type="RepaymentChange" />
        <BaseAttachmentList
          v-model="changeForm.attachmentList"
          :business-id="changeForm.id"
          business-type="FCT_REPAYMENT_PLAN_CHANGE"
          edit-mode
        />
      </a-form>
    </div>
    <WorkflowPreviewModal />
  </BasicPopup>
</template>

<style></style>
